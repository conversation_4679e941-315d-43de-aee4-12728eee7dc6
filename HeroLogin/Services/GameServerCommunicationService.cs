using System;
using System.Threading.Tasks;
using Grpc.Core;
using HeroLogin.Protos;
using Microsoft.Extensions.Logging;

namespace HeroLogin.Services
{
    /// <summary>
    /// Service xử lý giao tiếp với GameServer thông qua GRPC
    /// Thay thế cho TCP communication cũ
    /// </summary>
    public class GameServerCommunicationService : GameServerCommunication.GameServerCommunicationBase
    {
        private readonly ILogger<GameServerCommunicationService> _logger;

        public GameServerCommunicationService(ILogger<GameServerCommunicationService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// X<PERSON> lý message từ GameServer - tương đương với DataReceived() cũ
        /// </summary>
        public override async Task<GameServerMessageResponse> SendMessage(GameServerMessageRequest request, ServerCallContext context)
        {
            try
            {
                _logger.LogInformation($"Nhận message từ GameServer {request.ServerName} (ID: {request.ServerId}): {request.MessageType}");

                // Log raw message để debug
                if (!string.IsNullOrEmpty(request.RawMessage))
                {
                    _logger.LogDebug($"Raw message: {request.RawMessage}");
                }

                // Xử lý message dựa trên type
                var result = await ProcessMessage(request);

                return new GameServerMessageResponse
                {
                    Success = result.Success,
                    Message = result.Message,
                    ErrorCode = result.ErrorCode
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi xử lý message từ GameServer: {ex.Message}");
                return new GameServerMessageResponse
                {
                    Success = false,
                    Message = "Lỗi hệ thống",
                    ErrorCode = "INTERNAL_ERROR"
                };
            }
        }

        /// <summary>
        /// Xử lý message có response data
        /// </summary>
        public override async Task<GameServerMessageWithDataResponse> SendMessageWithResponse(GameServerMessageRequest request, ServerCallContext context)
        {
            try
            {
                _logger.LogInformation($"Nhận message với response từ GameServer {request.ServerName}: {request.MessageType}");

                var result = await ProcessMessageWithResponse(request);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi xử lý message với response: {ex.Message}");
                return new GameServerMessageWithDataResponse
                {
                    Success = false,
                    Message = "Lỗi hệ thống",
                    ErrorCode = "INTERNAL_ERROR"
                };
            }
        }

        /// <summary>
        /// Stream để gửi messages đến GameServer
        /// </summary>
        public override async Task ReceiveMessages(GameServerStreamRequest request, IServerStreamWriter<GameServerMessageResponse> responseStream, ServerCallContext context)
        {
            _logger.LogInformation($"GameServer {request.ServerName} đã kết nối stream");

            try
            {
                // Giữ kết nối stream cho đến khi client disconnect
                while (!context.CancellationToken.IsCancellationRequested)
                {
                    // Đợi 1 giây trước khi check lại
                    await Task.Delay(1000, context.CancellationToken);
                    
                    // TODO: Implement logic để gửi messages đến GameServer khi cần
                    // Ví dụ: khi có user login, kickout, etc.
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation($"GameServer {request.ServerName} đã ngắt kết nối stream");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi trong stream với GameServer {request.ServerName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý message và trả về kết quả đơn giản
        /// </summary>
        private async Task<(bool Success, string Message, string ErrorCode)> ProcessMessage(GameServerMessageRequest request)
        {
            // Chuyển đổi message type từ string sang enum nếu cần
            var messageType = request.MessageType.ToUpper();

            switch (messageType)
            {
                case "SERVER_CONNECT_LOGIN_X":
                    return await ProcessServerConnect(request);

                case "UPDATE_SERVER_PORT":
                    return await ProcessUpdateServerPort(request);

                case "REVIEW_USER_LOGIN":
                    return await ProcessReviewUserLogin(request);

                case "CROSS_SERVER_ZONE_INFO":
                    return await ProcessCrossServerZoneInfo(request);

                case "CROSS_SERVER_ZONE_REMOVE":
                    return await ProcessCrossServerZoneRemove(request);

                case "CROSS_SERVER_ZONE_ACTION":
                    return await ProcessCrossServerZoneAction(request);

                case "GROUP_QUEST_MESSAGE":
                    return await ProcessGroupQuestMessage(request);

                case "CHAT_GUILD":
                    return await ProcessChatGuild(request);

                case "USER_LOGIN_X":
                    return await ProcessUserLogin(request);

                case "USER_KICKOUT":
                case "USER_KICKOUTID":
                    return await ProcessUserKickout(request);

                default:
                    _logger.LogWarning($"Message type không được hỗ trợ: {messageType}");
                    return (false, $"Message type '{messageType}' không được hỗ trợ", "UNSUPPORTED_MESSAGE_TYPE");
            }
        }

        /// <summary>
        /// Xử lý message có response data
        /// </summary>
        private async Task<GameServerMessageWithDataResponse> ProcessMessageWithResponse(GameServerMessageRequest request)
        {
            var messageType = request.MessageType.ToUpper();

            switch (messageType)
            {
                case "GET_SERVER_LIST":
                    return await ProcessGetServerList(request);

                default:
                    return new GameServerMessageWithDataResponse
                    {
                        Success = false,
                        Message = $"Message type '{messageType}' không hỗ trợ response data",
                        ErrorCode = "UNSUPPORTED_RESPONSE_TYPE"
                    };
            }
        }

        // Các method xử lý cụ thể sẽ được implement ở đây
        private async Task<(bool, string, string)> ProcessServerConnect(GameServerMessageRequest request)
        {
            // TODO: Implement logic tương tự như TCP version
            _logger.LogInformation($"GameServer {request.ServerName} đã kết nối");
            return (true, "Kết nối thành công", "");
        }

        private async Task<(bool, string, string)> ProcessUpdateServerPort(GameServerMessageRequest request)
        {
            // TODO: Implement
            return (true, "Cập nhật port thành công", "");
        }

        private async Task<(bool, string, string)> ProcessReviewUserLogin(GameServerMessageRequest request)
        {
            // TODO: Implement
            return (true, "Review user login thành công", "");
        }

        private async Task<(bool, string, string)> ProcessCrossServerZoneInfo(GameServerMessageRequest request)
        {
            // TODO: Implement
            return (true, "Xử lý zone info thành công", "");
        }

        private async Task<(bool, string, string)> ProcessCrossServerZoneRemove(GameServerMessageRequest request)
        {
            // TODO: Implement
            return (true, "Xóa zone thành công", "");
        }

        private async Task<(bool, string, string)> ProcessCrossServerZoneAction(GameServerMessageRequest request)
        {
            // TODO: Implement
            return (true, "Xử lý zone action thành công", "");
        }

        private async Task<(bool, string, string)> ProcessGroupQuestMessage(GameServerMessageRequest request)
        {
            // TODO: Implement
            return (true, "Xử lý group quest message thành công", "");
        }

        private async Task<(bool, string, string)> ProcessChatGuild(GameServerMessageRequest request)
        {
            // TODO: Implement
            return (true, "Xử lý chat guild thành công", "");
        }

        private async Task<(bool, string, string)> ProcessUserLogin(GameServerMessageRequest request)
        {
            // TODO: Implement
            return (true, "Xử lý user login thành công", "");
        }

        private async Task<(bool, string, string)> ProcessUserKickout(GameServerMessageRequest request)
        {
            // TODO: Implement
            return (true, "Xử lý user kickout thành công", "");
        }

        private async Task<GameServerMessageWithDataResponse> ProcessGetServerList(GameServerMessageRequest request)
        {
            // TODO: Implement
            return new GameServerMessageWithDataResponse
            {
                Success = true,
                Message = "Lấy server list thành công",
                ErrorCode = "",
                ResponseData = { "server1", "server2" }, // Example data
                ResponseType = "SERVER_LIST"
            };
        }
    }
}
