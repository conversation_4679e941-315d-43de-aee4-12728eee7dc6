using System;
using System.Threading.Tasks;
using Grpc.Core;
using HeroLogin.Core;
using HeroLogin.Protos;
using HeroLogin.Database.Entities.Account;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HeroLogin.Services
{
    public class LoginAuthService : LoginAuth.LoginAuthBase
    {
        private readonly ILogger<LoginAuthService> _logger;
        private readonly DatabaseManager _databaseManager;
        private readonly ConfigManager _configManager;

        public LoginAuthService(ILogger<LoginAuthService> logger)
        {
            _logger = logger;
            _databaseManager = DatabaseManager.Instance;
            _configManager = ConfigManager.Instance;
        }

        public override async Task<VerifyTokenResponse> VerifyToken(VerifyTokenRequest request, ServerCallContext context)
        {
            _logger.LogInformation($"Yêu cầu xác thực token cho tài khoản {request.AccountId}");
            
            try
            {
                // Tìm tài khoản trong cơ sở dữ liệu
                var account = await _databaseManager.AccountDb.TblAccounts
                    .FirstOrDefaultAsync(a => a.FldId == request.AccountId);

                if (account == null)
                {
                    _logger.LogWarning($"Tài khoản {request.AccountId} không tồn tại");
                    return new VerifyTokenResponse
                    {
                        IsValid = false,
                        AccountId = request.AccountId,
                        ErrorMessage = "Tài khoản không tồn tại"
                    };
                }

                // Kiểm tra token
                if (account.FldPasskey != request.Token)
                {
                    _logger.LogWarning($"Token không hợp lệ cho tài khoản {request.AccountId}");
                    return new VerifyTokenResponse
                    {
                        IsValid = false,
                        AccountId = request.AccountId,
                        ErrorMessage = "Token không hợp lệ"
                    };
                }

                // Kiểm tra thời gian token
                if (!string.IsNullOrEmpty(account.FldPasskeyTimestamp))
                {
                    if (DateTime.TryParse(account.FldPasskeyTimestamp, out DateTime timestamp))
                    {
                        // Token hết hạn sau 5 phút
                        if ((DateTime.Now - timestamp).TotalMinutes > 5)
                        {
                            _logger.LogWarning($"Token đã hết hạn cho tài khoản {request.AccountId}");
                            return new VerifyTokenResponse
                            {
                                IsValid = false,
                                AccountId = request.AccountId,
                                ErrorMessage = "Token đã hết hạn"
                            };
                        }
                    }
                }

                _logger.LogInformation($"Xác thực token thành công cho tài khoản {request.AccountId}");
                return new VerifyTokenResponse
                {
                    IsValid = true,
                    AccountId = request.AccountId,
                    ErrorMessage = ""
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi xác thực token: {ex.Message}");
                return new VerifyTokenResponse
                {
                    IsValid = false,
                    AccountId = request.AccountId,
                    ErrorMessage = "Lỗi hệ thống"
                };
            }
        }

        public override async Task<RegisterGameServerResponse> RegisterGameServer(RegisterGameServerRequest request, ServerCallContext context)
        {
            _logger.LogInformation($"Yêu cầu đăng ký GameServer: {request.ServerName} (Cluster: {request.ClusterId}, Server: {request.ServerId})");
            
            try
            {
                // Tìm cluster và server trong cấu hình
                var cluster = _configManager.ServerClusterSettings.Find(c => c.ID == request.ClusterId);
                if (cluster == null)
                {
                    _logger.LogWarning($"Cluster {request.ClusterId} không tồn tại");
                    return new RegisterGameServerResponse
                    {
                        Success = false,
                        Message = $"Cluster {request.ClusterId} không tồn tại"
                    };
                }

                var server = cluster.Channels.Find(s => s.ServerID == request.ServerId);
                if (server == null)
                {
                    _logger.LogWarning($"Server {request.ServerId} không tồn tại trong cluster {request.ClusterId}");
                    return new RegisterGameServerResponse
                    {
                        Success = false,
                        Message = $"Server {request.ServerId} không tồn tại trong cluster {request.ClusterId}"
                    };
                }

                // Cập nhật thông tin server
                server.ServerName = request.ServerName;
                server.ServerIP = request.ServerIp;
                server.GameServerPort = request.ServerPort;
                server.GameServerGrpcPort = request.GrpcPort;
                server.Status = true;

                // Cập nhật thông tin trong cơ sở dữ liệu
                var serverStatus = await _databaseManager.AccountDb.TblOnlines
                    .FirstOrDefaultAsync(o => o.FldServer == request.ServerName && o.FldZone == request.ServerId);

                if (serverStatus == null)
                {
                    // Tạo mới nếu chưa tồn tại
                    serverStatus = new TblOnline
                    {
                        FldServer = request.ServerName,
                        FldZone = request.ServerId,
                        FldNowuser = 0,
                        FldMaxuser = server.MaximumOnline
                    };
                    _databaseManager.AccountDb.TblOnlines.Add(serverStatus);
                }
                else
                {
                    // Cập nhật nếu đã tồn tại
                    serverStatus.FldMaxuser = server.MaximumOnline;
                    serverStatus.FldNowuser = 0;
                }

                await _databaseManager.AccountDb.SaveChangesAsync();

                _logger.LogInformation($"Đăng ký GameServer thành công: {request.ServerName}");
                return new RegisterGameServerResponse
                {
                    Success = true,
                    Message = "Đăng ký GameServer thành công"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi đăng ký GameServer: {ex.Message}");
                return new RegisterGameServerResponse
                {
                    Success = false,
                    Message = $"Lỗi hệ thống: {ex.Message}"
                };
            }
        }

        public override async Task<UpdateGameServerStatusResponse> UpdateGameServerStatus(UpdateGameServerStatusRequest request, ServerCallContext context)
        {
            _logger.LogInformation($"Yêu cầu cập nhật trạng thái GameServer: Cluster {request.ClusterId}, Server {request.ServerId}, Online: {request.IsOnline}, Count: {request.OnlineCount}");
            
            try
            {
                // Tìm cluster và server trong cấu hình
                var cluster = _configManager.ServerClusterSettings.Find(c => c.ID == request.ClusterId);
                if (cluster == null)
                {
                    _logger.LogWarning($"Cluster {request.ClusterId} không tồn tại");
                    return new UpdateGameServerStatusResponse
                    {
                        Success = false,
                        Message = $"Cluster {request.ClusterId} không tồn tại"
                    };
                }

                var server = cluster.Channels.Find(s => s.ServerID == request.ServerId);
                if (server == null)
                {
                    _logger.LogWarning($"Server {request.ServerId} không tồn tại trong cluster {request.ClusterId}");
                    return new UpdateGameServerStatusResponse
                    {
                        Success = false,
                        Message = $"Server {request.ServerId} không tồn tại trong cluster {request.ClusterId}"
                    };
                }

                // Cập nhật trạng thái server
                server.Status = request.IsOnline;

                // Cập nhật thông tin trong cơ sở dữ liệu
                var serverStatus = await _databaseManager.AccountDb.TblOnlines
                    .FirstOrDefaultAsync(o => o.FldServer == server.ServerName && o.FldZone == request.ServerId);

                if (serverStatus == null)
                {
                    // Tạo mới nếu chưa tồn tại
                    serverStatus = new TblOnline
                    {
                        FldServer = server.ServerName,
                        FldZone = request.ServerId,
                        FldNowuser = request.OnlineCount,
                        FldMaxuser = server.MaximumOnline
                    };
                    _databaseManager.AccountDb.TblOnlines.Add(serverStatus);
                }
                else
                {
                    // Cập nhật nếu đã tồn tại
                    serverStatus.FldNowuser = request.OnlineCount;
                }

                await _databaseManager.AccountDb.SaveChangesAsync();

               // _logger.LogInformation($"Cập nhật trạng thái GameServer thành công: {server.ServerName}");
                return new UpdateGameServerStatusResponse
                {
                    Success = true,
                    Message = "Cập nhật trạng thái GameServer thành công"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Lỗi khi cập nhật trạng thái GameServer: {ex.Message}");
                return new UpdateGameServerStatusResponse
                {
                    Success = false,
                    Message = $"Lỗi hệ thống: {ex.Message}"
                };
            }
        }
    }
}
