﻿using System;
using System.Threading.Tasks;
using HeroLogin.Core;
using HeroLogin.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace HeroLogin
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            // Khởi tạo logger
            var logger = Logger.Instance;
            logger.Info("HeroLogin đang khởi động...");

            try
            {
                // Khởi tạo cấu hình
                var configManager = ConfigManager.Instance;
                logger.Info("Đã tải cấu hình thành công");

                // Khởi động LoginServer
                var loginServer = LoginServer.Instance;
                bool success = await loginServer.StartAsync();

                if (success)
                {
                    logger.Info("LoginServer đã khởi động thành công");

                    // Khởi động gRPC server
                    await CreateHostBuilder(args).Build().RunAsync();
                }
                else
                {
                    logger.Error("Không thể khởi động LoginServer");
                    return;
                }
            }
            catch (Exception ex)
            {
                logger.Error($"Lỗi khi khởi động ứng dụng: {ex.Message}");
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.ConfigureKestrel(options =>
                    {
                        var configManager = ConfigManager.Instance;
                        var grpcPort = configManager.LoginServerSettings.LoginServerGrpcPort;

                        // Cấu hình Kestrel để lắng nghe kết nối gRPC
                        options.ListenAnyIP(grpcPort, listenOptions =>
                        {
                            listenOptions.Protocols = HttpProtocols.Http2;
                        });
                    });

                    webBuilder.ConfigureServices(services =>
                    {
                        services.AddGrpc();
                    });

                    webBuilder.Configure(app =>
                    {
                        app.UseRouting();

                        app.UseEndpoints(endpoints =>
                        {
                            endpoints.MapGrpcService<LoginAuthService>();
                        });
                    });
                });
    }
}
