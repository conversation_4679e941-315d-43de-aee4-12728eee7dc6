﻿"restore":{"projectUniqueName":"/home/<USER>/Documents/HeroLinuxNet9/HeroLogin/HeroLogin.csproj","projectName":"HeroLogin","projectPath":"/home/<USER>/Documents/HeroLinuxNet9/HeroLogin/HeroLogin.csproj","outputPath":"/home/<USER>/Documents/HeroLinuxNet9/HeroLogin/obj/","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Akka":{"target":"Package","version":"[1.5.42, )"},"Akka.Remote":{"target":"Package","version":"[1.5.42, )"},"Akka.Streams":{"target":"Package","version":"[1.5.42, )"},"Grpc.AspNetCore":{"target":"Package","version":"[2.71.0, )"},"Microsoft.EntityFrameworkCore.Design":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[9.0.5, )"},"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[9.0.5, )"},"Microsoft.Extensions.Configuration":{"target":"Package","version":"[9.0.5, )"},"Microsoft.Extensions.Configuration.Binder":{"target":"Package","version":"[9.0.5, )"},"Microsoft.Extensions.Configuration.Json":{"target":"Package","version":"[9.0.5, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"/usr/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}