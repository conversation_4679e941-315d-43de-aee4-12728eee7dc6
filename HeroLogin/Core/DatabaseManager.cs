using Microsoft.EntityFrameworkCore;
using HeroLogin.Services;
using HeroLogin.Database.Entities.Account;

namespace HeroLogin.Core
{
    public class DatabaseManager
    {
        private static DatabaseManager? _instance;
        private readonly ConfigManager _configManager;

        private AccountDbContext? _accountDbContext;

        public static DatabaseManager Instance => _instance ??= new DatabaseManager();

        public AccountDbContext AccountDb => _accountDbContext ?? throw new InvalidOperationException("AccountDbContext chưa được khởi tạo");

        private DatabaseManager()
        {
            _configManager = ConfigManager.Instance;
        }

        public bool Initialize()
        {
            try
            {
                // Khởi tạo kết nối đến Account Database
                var accountOptions = new DbContextOptionsBuilder<AccountDbContext>()
                    .UseSqlServer(_configManager.ConnectionStrings.AccountDb)
                    .Options;
                _accountDbContext = new AccountDbContext(accountOptions);

                // Kiểm tra kết nối
                if (TestConnections())
                {
                    Logger.Instance.Info("Đã khởi tạo kết nối đến cơ sở dữ liệu thành công");
                    return true;
                }
                else
                {
                    Logger.Instance.Error("Không thể kết nối đến cơ sở dữ liệu");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi tạo kết nối cơ sở dữ liệu: {ex.Message}");
                return false;
            }
        }

        private bool TestConnections()
        {
            try
            {
                // Kiểm tra kết nối đến Account Database
                _accountDbContext?.Database.CanConnect();
                Logger.Instance.Info("Kết nối đến Account Database thành công");

                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi kiểm tra kết nối cơ sở dữ liệu: {ex.Message}");
                return false;
            }
        }
    }
}
