using System;
using System.Threading.Tasks;
using HeroLogin.Core.Actors;
using HeroLogin.Services;

namespace HeroLogin.Core
{
    public class LoginServer
    {
        private static LoginServer? _instance;
        private readonly ConfigManager _configManager;
        private readonly DatabaseManager _databaseManager;
        private readonly ActorSystemManager _actorSystemManager;
        private bool _isRunning = false;

        public static LoginServer Instance => _instance ??= new LoginServer();

        public bool IsRunning => _isRunning;

        private LoginServer()
        {
            _configManager = ConfigManager.Instance;
            _databaseManager = DatabaseManager.Instance;
            _actorSystemManager = ActorSystemManager.Instance;
        }

        public Task<bool> StartAsync()
        {
            if (_isRunning)
            {
                Logger.Instance.Warning("LoginServer đã đang chạy");
                return Task.FromResult(true);
            }

            try
            {
                Logger.Instance.Info("Đang khởi động LoginServer...");

                // Khởi tạo kết nối cơ sở dữ liệu
                if (!_databaseManager.Initialize())
                {
                    Logger.Instance.Error("Không thể khởi tạo kết nối cơ sở dữ liệu");
                    return Task.FromResult(false);
                }

                // Khởi tạo ActorSystem
                _actorSystemManager.Initialize();

                _isRunning = true;
                Logger.Instance.Info("LoginServer đã khởi động thành công");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi động LoginServer: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        public Task<bool> StopAsync()
        {
            if (!_isRunning)
            {
                Logger.Instance.Warning("LoginServer không chạy");
                return Task.FromResult(true);
            }

            try
            {
                Logger.Instance.Info("Đang dừng LoginServer...");

                // Dừng ActorSystem
                _actorSystemManager.Shutdown();

                _isRunning = false;
                Logger.Instance.Info("LoginServer đã dừng thành công");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng LoginServer: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        public async Task<bool> RestartAsync()
        {
            Logger.Instance.Info("Đang khởi động lại LoginServer...");

            bool stopResult = await StopAsync();
            if (!stopResult)
            {
                Logger.Instance.Error("Không thể dừng LoginServer để khởi động lại");
                return false;
            }

            // Đợi một chút để đảm bảo tất cả các tài nguyên đã được giải phóng
            await Task.Delay(1000);

            bool startResult = await StartAsync();
            if (!startResult)
            {
                Logger.Instance.Error("Không thể khởi động lại LoginServer");
                return false;
            }

            Logger.Instance.Info("LoginServer đã khởi động lại thành công");
            return true;
        }
    }
}
