using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Akka.Actor;
using HeroLogin.Services;
using Microsoft.EntityFrameworkCore;

namespace HeroLogin.Core.Actors
{
    // Message classes cho việc quản lý tham chiếu TcpManagerActor
    public class SetTcpManagerActor
    {
        public IActorRef TcpManagerActor { get; }

        public SetTcpManagerActor(IActorRef tcpManagerActor)
        {
            TcpManagerActor = tcpManagerActor;
        }
    }

    public class CheckTcpManagerActor { }
    /// <summary>
    /// Actor xử lý các gói tin
    /// </summary>
    public class PacketHandlerActor : ReceiveActor
    {
        private readonly Dictionary<PacketType, Func<IActorRef, ClientSession, Packet, Task>> _handlers = new();
        private readonly ConfigManager _configManager;
        private readonly DatabaseManager _databaseManager;
        private IActorRef? _tcpManagerActor;

        public PacketHandlerActor()
        {
            _configManager = ConfigManager.Instance;
            _databaseManager = DatabaseManager.Instance;

            // Đăng ký các message handler
            ReceiveAsync<ProcessPacket>(HandlePacketAsync);
            Receive<SetTcpManagerActor>(msg => {
                _tcpManagerActor = msg.TcpManagerActor;
                Logger.Instance.Debug("Đã nhận tham chiếu đến TcpManagerActor");
            });
            Receive<CheckTcpManagerActor>(_ => {
                if (_tcpManagerActor == null)
                {
                    try {
                        // Thử lấy tham chiếu đến TcpManagerActor
                        var selection = Context.System.ActorSelection("/user/tcpManager");
                        selection.Tell(new Identify(Guid.NewGuid()), Self);
                        Logger.Instance.Debug("Đã gửi yêu cầu xác định TcpManagerActor");
                    } catch (Exception ex) {
                        Logger.Instance.Error($"Không thể gửi yêu cầu xác định TcpManagerActor: {ex.Message}");
                    }
                }
            });
            Receive<ActorIdentity>(msg => {
                if (msg.Subject != null)
                {
                    _tcpManagerActor = msg.Subject;
                    Logger.Instance.Debug("Đã xác định được TcpManagerActor");
                }
                else
                {
                    Logger.Instance.Warning("Không thể xác định TcpManagerActor, sẽ sử dụng ActorSelection");
                }
            });

            // Đăng ký các packet handler
            RegisterHandlers();

            // Lên lịch kiểm tra tham chiếu TcpManagerActor sau 2 giây
            Context.System.Scheduler.ScheduleTellOnce(
                TimeSpan.FromSeconds(2),
                Self,
                new CheckTcpManagerActor(),
                Self);
        }

        protected override void PreStart()
        {
            base.PreStart();
            Logger.Instance.Debug("PacketHandlerActor đã được khởi động");
        }

        private void RegisterHandlers()
        {
            // Đăng ký các handler cho từng loại gói tin
            _handlers[PacketType.Ping] = HandlePingAsync;
            _handlers[PacketType.Login] = HandleLoginAsync;
            _handlers[PacketType.ServerList] = HandleServerListAsync;
            _handlers[PacketType.SelectServer] = HandleSelectServerAsync;
            _handlers[PacketType.Disconnect] = HandleDisconnectAsync;
            _handlers[PacketType.ServerList2] = HandleServerListAsync;
        }

        private async Task HandlePacketAsync(ProcessPacket message)
        {
            try
            {
                var packet = Packet.Parse(message.Data, message.Data.Length);

                // Ghi log gói tin đã phân tích
                PacketLogger.LogPacket(message.Session.SessionId, packet, true);

                if (_handlers.TryGetValue(packet.Type, out var handler))
                {
                    await handler(message.Connection, message.Session, packet);
                }
                else
                {
                    Logger.Instance.Warning($"Không có handler cho gói tin loại {packet.Type} từ session {message.Session.SessionId}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin từ session {message.Session.SessionId}: {ex.Message}");
            }
        }

        private async Task HandlePingAsync(IActorRef connection, ClientSession session, Packet packet)
        {
            // Xử lý gói tin Ping
            // Gửi phản hồi Ping
            var response = new Packet(PacketType.Ping, Array.Empty<byte>());
            await SendPacketAsync(connection, response, session.SessionId);
        }

        private async Task HandleLoginAsync(IActorRef connection, ClientSession session, Packet packet)
        {
            // Xử lý gói tin Login
            // Giả sử dữ liệu gói tin: [username length][username][password length][password]
            try
            {
                int usernameLength = BitConverter.ToInt16(packet.Data, 0);
                string username = Encoding.GetEncoding(1252).GetString(packet.Data, 2, usernameLength);

                int passwordLength = BitConverter.ToInt16(packet.Data, 2 + usernameLength);
                string password = Encoding.GetEncoding(1252).GetString(packet.Data, 4 + usernameLength, passwordLength);

                Logger.Instance.Debug($"Yêu cầu đăng nhập từ {username} - {password}");

                // Xác thực người dùng với cơ sở dữ liệu
                bool isAuthenticated = await AuthenticateUserAsync(username, password);

                if (isAuthenticated)
                {
                    Logger.Instance.Info("Đăng nhập thành công");
                    // Đăng nhập thành công
                    session.IsAuthenticated = true;
                    session.AccountId = username;

                    // Gửi phản hồi thành công
                    var responseData = Builder.BuildLoginSuccessResponse(username, password);
                    var response = new Packet(PacketType.LoginResponse, responseData);

                    Logger.Instance.Debug($"Chuẩn bị gửi gói tin đăng nhập thành công cho {username}");
                    Logger.Instance.Debug($"Chi tiết gói tin: Type={response.Type}, Length={response.Data.Length}");
                    Logger.Instance.Debug($"Dữ liệu gói tin: {BitConverter.ToString(response.ToByteArray())}");

                    try {
                        await SendPacketAsync(connection, response, session.SessionId);
                        Logger.Instance.Debug($"Đã gọi SendPacketAsync cho gói tin đăng nhập thành công");
                    } catch (Exception ex) {
                        Logger.Instance.Error($"Lỗi khi gửi gói tin đăng nhập: {ex.Message}");
                        Logger.Instance.Error($"Stack trace: {ex.StackTrace}");
                    }

                    Logger.Instance.Info($"Người dùng {username} đã đăng nhập thành công");
                }
                else
                {
                    Logger.Instance.Info("Đăng nhập thất bại");
                    // Đăng nhập thất bại
                    // Gửi phản hồi thất bại
                    byte[] responseData = BitConverter.GetBytes(0); // 0 = thất bại
                    var response = new Packet(PacketType.LoginResponse, responseData);
                    await SendPacketAsync(connection, response, session.SessionId);

                    Logger.Instance.Info($"Đăng nhập thất bại cho người dùng {username}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin đăng nhập: {ex.Message}");

                // Gửi phản hồi thất bại
                byte[] responseData = BitConverter.GetBytes(0); // 0 = thất bại
                var response = new Packet(PacketType.Login, responseData);
                await SendPacketAsync(connection, response, session.SessionId);
            }
        }

        private async Task<bool> AuthenticateUserAsync(string username, string password)
        {
            try
            {
                // Tìm tài khoản trong cơ sở dữ liệu
                var account = await _databaseManager.AccountDb.TblAccounts
                    .FirstOrDefaultAsync(a => a.FldId == username && a.FldPassword == password);

                if (account == null)
                {
                    return false;
                }

                // Kiểm tra xem tài khoản có bị khóa không
                if (account.FldLock == 1)
                {
                    Logger.Instance.Warning($"Tài khoản {username} đã bị khóa");
                    return false;
                }

                // Cập nhật thông tin đăng nhập
                account.FldLastlogintime = DateTime.Now;
                account.FldOnline = 1;
                await _databaseManager.AccountDb.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xác thực người dùng: {ex.Message}");
                return false;
            }
        }

        private async Task HandleServerListAsync(IActorRef connection, ClientSession session, Packet packet)
        {
            // Kiểm tra xem người dùng đã đăng nhập chưa
            if (!session.IsAuthenticated)
            {
                Logger.Instance.Warning($"Session {session.SessionId} yêu cầu danh sách máy chủ nhưng chưa đăng nhập");
                return;
            }

            try
            {
                // Lấy danh sách máy chủ từ cấu hình
                var serverClusters = _configManager.ServerClusterSettings;
                
                // Cập nhật số người chơi hiện tại cho mỗi kênh
                foreach (var cluster in serverClusters)
                {
                    foreach (var channel in cluster.Channels)
                    {
                        try
                        {
                            var serverStatus = await _databaseManager.AccountDb.TblOnlines
                                .FirstOrDefaultAsync(o => o.FldServer == channel.ServerName && o.FldZone == channel.ServerID);

                            channel.CurrentPlayers = serverStatus?.FldNowuser ?? 101;
                        }
                        catch (Exception ex)
                        {
                            Logger.Instance.Error($"Lỗi khi lấy thông tin người chơi trực tuyến: {ex.Message}");
                            channel.CurrentPlayers = 0;
                        }
                    }
                }

                // Sử dụng Builder để tạo dữ liệu phản hồi
                var responseData = Builder.BuildServerListResponse(serverClusters);
                
                // Tạo gói tin phản hồi
                var response = new Packet(PacketType.ServerListResponse, responseData);
                await SendPacketAsync(connection, response, session.SessionId);

                Logger.Instance.Debug($"Đã gửi {serverClusters.Count} cluster và tổng cộng {serverClusters.Sum(c => c.Channels.Count)} kênh máy chủ đến session {session.SessionId}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý yêu cầu danh sách máy chủ: {ex.Message}");
            }
        }

        private async Task HandleSelectServerAsync(IActorRef connection, ClientSession session, Packet packet)
        {
            // Kiểm tra xem người dùng đã đăng nhập chưa
            if (!session.IsAuthenticated)
            {
                Logger.Instance.Warning($"Session {session.SessionId} yêu cầu chọn máy chủ nhưng chưa đăng nhập");
                return;
            }

            try
            {
                // Đọc ID của cluster và kênh
                int clusterId = BitConverter.ToInt32(packet.Data, 0);
                int channelId = BitConverter.ToInt32(packet.Data, 4);

                // Tìm thông tin máy chủ
                var cluster = _configManager.ServerClusterSettings.FirstOrDefault(c => c.ID == clusterId);
                if (cluster == null)
                {
                    Logger.Instance.Warning($"Session {session.SessionId} chọn cluster không tồn tại: {clusterId}");
                    return;
                }

                var channel = cluster.Channels.FirstOrDefault(ch => ch.ServerID == channelId);
                if (channel == null)
                {
                    Logger.Instance.Warning($"Session {session.SessionId} chọn kênh không tồn tại: {channelId}");
                    return;
                }

                // Lưu thông tin máy chủ đã chọn
                session.SelectedServerId = $"{clusterId}_{channelId}";

                // Tạo token xác thực cho GameServer
                string authToken = Guid.NewGuid().ToString();

                // Lưu token vào cơ sở dữ liệu
                var account = await _databaseManager.AccountDb.TblAccounts
                    .FirstOrDefaultAsync(a => a.FldId == session.AccountId);

                if (account != null)
                {
                    account.FldPasskey = authToken;
                    account.FldPasskeyTimestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    await _databaseManager.AccountDb.SaveChangesAsync();
                }

                // Tạo dữ liệu phản hồi
                var responseData = Builder.BuildSelectServerReponse(clusterId, channel);

                // Tạo gói tin phản hồi
                var response = new Packet(PacketType.SelectServerResponse, responseData);
                await SendPacketAsync(connection, response, session.SessionId);

                Logger.Instance.Info($"Session {session.SessionId} đã chọn máy chủ {cluster.ClusterName} - {channel.ServerName}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý yêu cầu chọn máy chủ: {ex.Message}");
            }
        }

        private async Task HandleDisconnectAsync(IActorRef connection, ClientSession session, Packet packet)
        {
            try
            {
                // Cập nhật trạng thái đăng nhập nếu người dùng đã đăng nhập
                if (session.IsAuthenticated && !string.IsNullOrEmpty(session.AccountId))
                {
                    var account = await _databaseManager.AccountDb.TblAccounts
                        .FirstOrDefaultAsync(a => a.FldId == session.AccountId);

                    if (account != null)
                    {
                        account.FldOnline = 0;
                        await _databaseManager.AccountDb.SaveChangesAsync();
                    }
                }

                // Đóng kết nối
                connection.Tell(Akka.IO.Tcp.Close.Instance);

                Logger.Instance.Info($"Session {session.SessionId} đã ngắt kết nối");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý yêu cầu ngắt kết nối: {ex.Message}");
            }
        }

        // Helper method để gửi gói tin
        private Task SendPacketAsync(IActorRef connection, byte[] data)
        {
            // Gửi gói tin đến TcpManagerActor
            if (_tcpManagerActor != null)
            {
                _tcpManagerActor.Tell(new SendPacket(connection, data));
                Logger.Instance.Debug("Gửi gói tin đến TcpManagerActor thông qua tham chiếu trực tiếp");
            }
            else
            {
                // Fallback nếu không có tham chiếu trực tiếp
                var tcpManagerRef = ActorSystemManager.Instance.TcpManagerActor;
                tcpManagerRef.Tell(new SendPacket(connection, data));
                Logger.Instance.Debug("Gửi gói tin đến TcpManagerActor thông qua ActorSystemManager");
            }
            return Task.CompletedTask;
        }

        // Helper method để gửi gói tin đã được phân tích
        private Task SendPacketAsync(IActorRef connection, Packet packet, int sessionId)
        {
            // Ghi log gói tin đã phân tích
            PacketLogger.LogPacket(sessionId, packet, false);

            // Chuẩn bị dữ liệu gói tin
            var packetData = packet.ToByteArray();

            // Gửi gói tin đến TcpManagerActor
            if (_tcpManagerActor != null)
            {
                _tcpManagerActor.Tell(new SendPacket(connection, packetData));
                Logger.Instance.Debug($"Đã gửi gói tin {packet.Type} đến TcpManagerActor thông qua tham chiếu trực tiếp");
            }
            else
            {
                // Fallback nếu không có tham chiếu trực tiếp
                var tcpManagerRef = ActorSystemManager.Instance.TcpManagerActor;
                tcpManagerRef.Tell(new SendPacket(connection, packetData));
                Logger.Instance.Debug($"Đã gửi gói tin {packet.Type} đến TcpManagerActor thông qua ActorSystemManager");
            }

            return Task.CompletedTask;
        }
    }
}
