using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Runtime.CompilerServices;
using Avalonia.Media;
using Avalonia.Threading;

namespace HeroYulgang.Services
{
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Fatal
    }

    public class LogEntry : INotifyPropertyChanged
    {
        private DateTime _timestamp;
        private LogLevel _level;
        private string _message = string.Empty;

        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                if (_timestamp != value)
                {
                    _timestamp = value;
                    OnPropertyChanged();
                }
            }
        }

        public LogLevel Level
        {
            get => _level;
            set
            {
                if (_level != value)
                {
                    _level = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(Color));
                }
            }
        }

        public string Message
        {
            get => _message;
            set
            {
                if (_message != value)
                {
                    _message = value;
                    OnPropertyChanged();
                }
            }
        }

        public IBrush Color => Level switch
        {
            LogLevel.Debug => Brushes.Gray,
            LogLevel.Info => Brushes.Green,
            LogLevel.Warning => Brushes.Orange,
            LogLevel.Error => Brushes.Red,
            LogLevel.Fatal => Brushes.DarkRed,
            _ => Brushes.Black
        };

        public string LevelName => Level switch
        {
            LogLevel.Debug => "Debug",
            LogLevel.Info => "Thông tin",
            LogLevel.Warning => "Cảnh báo",
            LogLevel.Error => "Lỗi",
            LogLevel.Fatal => "Nghiêm trọng",
            _ => Level.ToString()
        };

        public string FormattedMessage => $"[{Timestamp:yyyy-MM-dd HH:mm:ss}] [{LevelName}] {Message}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class Logger : INotifyPropertyChanged
    {
        private static Logger? _instance;
        private readonly ObservableCollection<LogEntry> _logs = [];
        private readonly string _logDirectory;
        private string _currentLogFile = string.Empty;

        public static Logger Instance => _instance ??= new Logger();

        public ObservableCollection<LogEntry> Logs => _logs;

        private Logger()
        {
            // Private constructor for singleton pattern
            _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");

            // Ghi thông tin về đường dẫn thư mục log vào console để debug
            Console.WriteLine($"Thư mục log: {_logDirectory}");

            // Đảm bảo thư mục logs tồn tại
            if (!Directory.Exists(_logDirectory))
            {
                try
                {
                    Directory.CreateDirectory(_logDirectory);
                    Console.WriteLine("Đã tạo thư mục logs");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Lỗi khi tạo thư mục logs: {ex.Message}");
                }
            }

            // Khởi tạo tên file log theo ngày hiện tại
            UpdateLogFileName();
        }

        private void UpdateLogFileName()
        {
            try
            {
                var today = DateTime.Now;
                string fileName = $"log_{today:yyyy-MM-dd}.txt";
                _currentLogFile = Path.Combine(_logDirectory, fileName);

                // Kiểm tra xem file có tồn tại không, nếu không thì tạo file mới
                if (!File.Exists(_currentLogFile))
                {
                    // Tạo file mới với header
                    using var writer = new StreamWriter(_currentLogFile, false);
                    writer.WriteLine($"=== Log file created at {today:yyyy-MM-dd HH:mm:ss} ===");
                    writer.Flush();
                    Console.WriteLine($"Đã tạo file log mới: {_currentLogFile}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi cập nhật tên file log: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        public void Log(LogLevel level, string message)
        {
            try
            {
                var timestamp = DateTime.Now;
                var entry = new LogEntry
                {
                    Timestamp = timestamp,
                    Level = level,
                    Message = message
                };

                // Thêm vào danh sách logs trong giao diện - đảm bảo thực hiện trên UI thread
                if (Dispatcher.UIThread.CheckAccess())
                {
                    // Nếu đang ở UI thread, thêm trực tiếp
                    _logs.Add(entry);
                    OnPropertyChanged(nameof(Logs));
                }
                else
                {
                    // Nếu không ở UI thread, chuyển sang UI thread
                    Dispatcher.UIThread.Post(() =>
                    {
                        _logs.Add(entry);
                        OnPropertyChanged(nameof(Logs));
                    });
                }

                // Kiểm tra xem ngày hiện tại có khác với ngày trong tên file log không
                string expectedFileName = $"log_{timestamp:yyyy-MM-dd}.txt";
                if (!_currentLogFile.EndsWith(expectedFileName))
                {
                    Console.WriteLine($"Cần cập nhật file log: {_currentLogFile} -> {expectedFileName}");
                    UpdateLogFileName();
                }

                // Lưu log vào file
                string formattedMessage = entry.FormattedMessage;
                WriteToLogFile(formattedMessage);
            }
            catch (Exception ex)
            {
                // Xử lý lỗi khi ghi log (không gọi lại Log để tránh vòng lặp vô hạn)
                Console.WriteLine($"Lỗi khi ghi log: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        private void WriteToLogFile(string logMessage)
        {
            try
            {
                // Đảm bảo thư mục logs tồn tại
                if (!Directory.Exists(_logDirectory))
                {
                    Directory.CreateDirectory(_logDirectory);
                }


                // Sử dụng StreamWriter với tham số append=true để thêm vào cuối file
                using var writer = new StreamWriter(_currentLogFile, false);
                writer.WriteLine(logMessage);
                writer.Flush(); // Đảm bảo dữ liệu được ghi ngay lập tức
            }
            catch (Exception ex)
            {
                // Xử lý lỗi khi ghi file (không ghi log để tránh vòng lặp vô hạn)
                Console.WriteLine($"Lỗi khi ghi log vào file: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        public void Debug(string message) => Log(LogLevel.Debug, message);
        public void Info(string message) => Log(LogLevel.Info, message);
        public void Warning(string message) => Log(LogLevel.Warning, message);
        public void Error(string message) => Log(LogLevel.Error, message);
        public void Fatal(string message) => Log(LogLevel.Fatal, message);

        public void Clear()
        {
            try
            {
                // Đảm bảo thực hiện trên UI thread
                if (Dispatcher.UIThread.CheckAccess())
                {
                    // Nếu đang ở UI thread, xóa trực tiếp
                    _logs.Clear();
                    OnPropertyChanged(nameof(Logs));
                }
                else
                {
                    // Nếu không ở UI thread, chuyển sang UI thread
                    Dispatcher.UIThread.Post(() =>
                    {
                        _logs.Clear();
                        OnPropertyChanged(nameof(Logs));
                    });
                }

                // Kiểm tra xem ngày hiện tại có khác với ngày trong tên file log không
                var timestamp = DateTime.Now;
                string expectedFileName = $"log_{timestamp:yyyy-MM-dd}.txt";
                if (!_currentLogFile.EndsWith(expectedFileName))
                {
                    UpdateLogFileName();
                }

                // Ghi thông báo vào file log khi xóa log
                string clearMessage = $"[{timestamp:yyyy-MM-dd HH:mm:ss}] [Thông tin] Log đã được xóa từ giao diện";
                Console.WriteLine($"Ghi thông báo xóa log: {clearMessage}");
                WriteToLogFile(clearMessage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi xóa log: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        public string GetCurrentLogFilePath()
        {
            return _currentLogFile;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
