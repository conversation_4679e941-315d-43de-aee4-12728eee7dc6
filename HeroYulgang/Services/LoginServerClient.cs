using System;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Threading.Tasks;
using Grpc.Net.Client;
using HeroYulgang.Core;
using HeroYulgang.Protos;

namespace HeroYulgang.Services
{
    public class LoginServerClient
    {
        private static LoginServerClient? _instance;
        private readonly ConfigManager _configManager;
        private readonly Logger _logger;
        private GrpcChannel? _channel;
        private LoginAuth.LoginAuthClient? _client;
        private bool _isConnected;
        private DateTime _lastConnectionAttempt = DateTime.MinValue;
        private readonly TimeSpan _reconnectInterval = TimeSpan.FromSeconds(30);

        public bool IsConnected => _isConnected;
        public static LoginServerClient Instance => _instance ??= new LoginServerClient();

        private LoginServerClient()
        {
            _configManager = ConfigManager.Instance;
            _logger = Logger.Instance;
            _isConnected = false;
        }

        public async Task<bool> ConnectAsync()
        {
            if (_isConnected && _client != null)
            {
                return true;
            }

            // <PERSON><PERSON><PERSON> tra thời gian kết nối lại
            if (DateTime.Now - _lastConnectionAttempt < _reconnectInterval)
            {
                return false;
            }

            _lastConnectionAttempt = DateTime.Now;

            try
            {
                var loginServerIp = _configManager.LoginServerSettings.LoginServerIP;
                var loginServerPort = _configManager.LoginServerSettings.LoginServerGrpcPort;
                var address = $"http://{loginServerIp}:{loginServerPort}";

                _logger.Info($"Đang kết nối đến LoginServer tại {address}...");

                // Tạo kênh gRPC
                _channel = GrpcChannel.ForAddress(address);
                _client = new LoginAuth.LoginAuthClient(_channel);

                // Kiểm tra kết nối bằng cách gọi một phương thức đơn giản
                await RegisterGameServerAsync();

                _isConnected = true;
                _logger.Info("Đã kết nối thành công đến LoginServer");
                return true;
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Không thể kết nối đến LoginServer: {ex.Message}");
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            if (_channel != null)
            {
                await _channel.ShutdownAsync();
                _channel = null;
                _client = null;
                _isConnected = false;
                _logger.Info("Đã ngắt kết nối từ LoginServer");
            }
        }

        public async Task<bool> RegisterGameServerAsync()
        {
            if (_client == null)
            {
                _logger.Error("Không thể đăng ký GameServer: Chưa kết nối đến LoginServer");
                return false;
            }

            try
            {
                var serverConfig = _configManager.ServerSettings;
                var loginConfig = _configManager.LoginServerSettings;
                
                // Lấy địa chỉ IP của máy chủ
                string serverIp = GetLocalIPAddress();

                var request = new RegisterGameServerRequest
                {
                    ClusterId = loginConfig.ClusterId,
                    ServerId = serverConfig.ServerId,
                    ServerName = serverConfig.ServerName,
                    ServerIp = serverIp,
                    ServerPort = serverConfig.GameServerPort,
                    GrpcPort = 0 // GameServer không cung cấp dịch vụ GRPC
                };

                var response = await _client.RegisterGameServerAsync(request);

                if (response.Success)
                {
                    _logger.Info($"Đăng ký GameServer thành công: {response.Message}");
                    return true;
                }
                else
                {
                    _logger.Error($"Đăng ký GameServer thất bại: {response.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Lỗi khi đăng ký GameServer: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateGameServerStatusAsync(int onlineCount, bool isOnline)
        {
            if (_client == null)
            {
                _logger.Error("Không thể cập nhật trạng thái GameServer: Chưa kết nối đến LoginServer");
                return false;
            }

            try
            {
                var serverConfig = _configManager.ServerSettings;
                var loginConfig = _configManager.LoginServerSettings;

                var request = new UpdateGameServerStatusRequest
                {
                    ClusterId = loginConfig.ClusterId,
                    ServerId = serverConfig.ServerId,
                    OnlineCount = onlineCount,
                    IsOnline = isOnline
                };

                var response = await _client.UpdateGameServerStatusAsync(request);

                if (response.Success)
                {
                    //_logger.Debug($"Cập nhật trạng thái GameServer thành công: {response.Message}");
                    return true;
                }
                else
                {
                    _logger.Warning($"Cập nhật trạng thái GameServer thất bại: {response.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Lỗi khi cập nhật trạng thái GameServer: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> VerifyTokenAsync(string accountId, string token)
        {
            if (_client == null)
            {
                _logger.Error("Không thể xác thực token: Chưa kết nối đến LoginServer");
                return false;
            }

            try
            {
                var request = new VerifyTokenRequest
                {
                    AccountId = accountId,
                    Token = token
                };

                var response = await _client.VerifyTokenAsync(request);

                if (response.IsValid)
                {
                    _logger.Info($"Xác thực token thành công cho tài khoản {accountId}");
                    return true;
                }
                else
                {
                    _logger.Warning($"Xác thực token thất bại cho tài khoản {accountId}: {response.ErrorMessage}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Lỗi khi xác thực token: {ex.Message}");
                return false;
            }
        }

        private string GetLocalIPAddress()
        {
            string localIP = "127.0.0.1";
            try
            {
                // Lấy địa chỉ IP của máy chủ
                using (Socket socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, 0))
                {
                    socket.Connect("*******", 65530);
                    if (socket.LocalEndPoint is System.Net.IPEndPoint endPoint)
                    {
                        localIP = endPoint.Address.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Warning($"Không thể lấy địa chỉ IP: {ex.Message}. Sử dụng 127.0.0.1");
            }
            return localIP;
        }
    }
}
