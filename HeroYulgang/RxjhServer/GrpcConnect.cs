using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer.GroupQuest;
using RxjhServer.HelperTools;

namespace RxjhServer
{
    /// <summary>
    /// GRPC-based Connect class thay thế cho TCP Connect.cs
    /// Giữ nguyên interface để tương thích với code hiện tại
    /// </summary>
    public partial class GrpcConnect
    {
        private System.Timers.Timer timer_0;
        private GameServerCommunicationClient _grpcClient;
        private bool _isConnected = false;

        public GrpcConnect()
        {
            _grpcClient = GameServerCommunicationClient.Instance;

            // Timer để kiểm tra kết nối định kỳ
            timer_0 = new(5000.0);
            timer_0.Elapsed += timer_0_Elapsed;
            timer_0.AutoReset = true;
            timer_0.Enabled = true;
        }

        private async void timer_0_Elapsed(object sender, ElapsedEventArgs e)
        {
            if (!_isConnected)
            {
                await SetupAsync();
            }
        }

        /// <summary>
        /// Thiết lập kết nối GRPC - thay thế cho Sestup()
        /// </summary>
        public async Task<bool> SetupAsync()
        {
            try
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info,
                    $"Đang kết nối đến LoginServer qua GRPC - IP: {World.AccountVerificationServerIP}, Port: {World.AccountVerificationServerPort}");

                var connected = await _grpcClient.ConnectAsync();
                if (connected)
                {
                    _isConnected = true;
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug,
                        "LoginServer đã kết nối thành công qua GRPC");

                    // Gửi các message khởi tạo
                    await SendInitialMessages();
                    return true;
                }
                else
                {
                    _isConnected = false;
                    return false;
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"Lỗi kết nối LoginServer qua GRPC: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gửi các message khởi tạo sau khi kết nối
        /// </summary>
        private async Task SendInitialMessages()
        {
            try
            {
                // Gửi message cập nhật port
                await Task.Delay(500);
                await TransmitAsync($"UPDATE_SERVER_PORT|{World.ServerID}|{World.GameServerPort2}");

                // Review user login
                await ReviewUserLoginAsync();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"Lỗi khi gửi message khởi tạo: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (timer_0 != null)
            {
                timer_0.Enabled = false;
                timer_0.Close();
                timer_0.Dispose();
            }

            _grpcClient?.Dispose();
            _isConnected = false;
        }

        /// <summary>
        /// Gửi thông tin review user login - async version
        /// </summary>
        public async Task ReviewUserLoginAsync()
        {
            try
            {
                StringBuilder stringBuilder = new();
                foreach (var value9 in World.list.Values)
                {
                    var value = "NULL";
                    var value2 = 0;
                    if (value9.TreoMay)
                    {
                        value2 = 1;
                    }
                    var value3 = 0;
                    var value4 = string.Empty;
                    var value5 = string.Empty;
                    var value6 = 0;
                    var value7 = string.Empty;
                    var value8 = string.Empty;
                    var players = World.FindPlayerBySession(value9.WorldId);
                    if (players != null)
                    {
                        value = players.UserName;
                        value3 = players.OriginalServerSerialNumber;
                        value4 = players.OriginalServerIP;
                        value5 = players.OriginalServerPort.ToString();
                        value6 = players.OriginalServerID;
                        value7 = players.SilverCoinSquareServerIP;
                        value8 = players.SilverCoinSquareServerPort.ToString();
                    }
                    if (value9.Player == null)
                    {
                        LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Player Null;");
                        return;
                    }
                    stringBuilder.Append(value9.Player.Userid);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value9);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value9.BindAccount);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value2);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value3);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value4);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value5);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value6);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value7);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value8);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value9.WorldId);
                    stringBuilder.Append(",");
                }
                if (stringBuilder.Length > 0)
                {
                    stringBuilder.Remove(stringBuilder.Length - 1, 1);
                }
                await TransmitAsync("REVIEW_USER_LOGIN|" + stringBuilder);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"Review Nguoi Choi Dang Nhap error: {ex.Message}");
            }
        }

        /// <summary>
        /// Transmit method - giữ nguyên signature để tương thích
        /// </summary>
        public void Transmit(string message)
        {
            // Chuyển sang async version
            _ = Task.Run(async () => await TransmitAsync(message));
        }

        /// <summary>
        /// Async version của Transmit
        /// </summary>
        public async Task<bool> TransmitAsync(string message)
        {
            try
            {
                if (!_isConnected)
                {
                    // Thử kết nối lại
                    var reconnected = await _grpcClient.EnsureConnectedAsync();
                    if (!reconnected)
                    {
                        LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Warning,
                            "Không thể gửi message - chưa kết nối đến LoginServer");
                        return false;
                    }
                    _isConnected = true;
                }

                var success = await _grpcClient.TransmitAsync(message);
                if (!success)
                {
                    _isConnected = false;
                }
                return success;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"GRPC Transmit error: {message} - {ex.Message}");
                _isConnected = false;
                return false;
            }
        }

        /// <summary>
        /// Transmit với response data
        /// </summary>
        public async Task<(bool Success, string[] ResponseData)> TransmitWithResponseAsync(string message)
        {
            try
            {
                if (!_isConnected)
                {
                    var reconnected = await _grpcClient.EnsureConnectedAsync();
                    if (!reconnected)
                    {
                        return (false, Array.Empty<string>());
                    }
                    _isConnected = true;
                }

                var result = await _grpcClient.TransmitWithResponseAsync(message);
                if (!result.Success)
                {
                    _isConnected = false;
                }
                return result;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"GRPC TransmitWithResponse error: {message} - {ex.Message}");
                _isConnected = false;
                return (false, Array.Empty<string>());
            }
        }

        /// <summary>
        /// Kiểm tra trạng thái kết nối
        /// </summary>
        public bool IsConnected => _isConnected && _grpcClient.IsConnected;

        /// <summary>
        /// Gửi hành động trong Zone liên server đến LoginServer
        /// </summary>
        public void SendCrossServerAction(int zoneId, string actionType, int sessionId, params object[] parameters)
        {
            try
            {
                // Tạo chuỗi thông tin hành động
                StringBuilder sb = new StringBuilder();
                sb.Append("CROSS_SERVER_ZONE_ACTION|");
                sb.Append(World.ServerID);
                sb.Append("|");
                sb.Append(zoneId);
                sb.Append("|");
                sb.Append(actionType);
                sb.Append("|");
                sb.Append(sessionId);

                // Thêm các tham số
                foreach (var param in parameters)
                {
                    sb.Append("|");
                    sb.Append(param);
                }

                // Gửi thông tin hành động đến LoginServer
                Transmit(sb.ToString());
                if (actionType != "GET_NPCS")
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi hành động {actionType} trong Zone {zoneId} đến LoginServer");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi hành động Zone liên server: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi dữ liệu broadcast trong Zone liên server đến LoginServer
        /// </summary>
        public void SendCrossServerBroadcast(int zoneId, string dataType, int sessionId, string hexData, int id, int wordid)
        {
            try
            {
                // Tạo chuỗi thông tin broadcast
                StringBuilder sb = new();
                sb.Append("CROSS_SERVER_ZONE_BROADCAST|");
                sb.Append(World.ServerID);
                sb.Append("|");
                sb.Append(zoneId);
                sb.Append("|");
                sb.Append(dataType);
                sb.Append("|");
                sb.Append(sessionId);
                sb.Append("|");
                sb.Append(hexData);
                sb.Append("|");
                sb.Append(id);
                sb.Append("|");
                sb.Append(wordid);

                // Gửi thông tin broadcast đến LoginServer
                Transmit(sb.ToString());

                //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi broadcast {dataType} trong Zone {zoneId} đến LoginServer");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi broadcast Zone liên server: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi dữ liệu broadcast full trong Zone liên server đến LoginServer
        /// </summary>
        public void SendCrossServerBroadcast(int zoneId, string dataType, int sessionId, string hexData)
        {
            try
            {
                // Tạo chuỗi thông tin broadcast
                StringBuilder sb = new();
                sb.Append("CROSS_SERVER_ZONE_BROADCAST_FULL|");
                sb.Append(World.ServerID);
                sb.Append("|");
                sb.Append(zoneId);
                sb.Append("|");
                sb.Append(dataType);
                sb.Append("|");
                sb.Append(sessionId);
                sb.Append("|");
                sb.Append(hexData);
                sb.Append("|");

                // Gửi thông tin broadcast đến LoginServer
                Transmit(sb.ToString());

                //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi broadcast {dataType} trong Zone {zoneId} đến LoginServer");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi broadcast Zone liên server: {ex.Message}");
            }
        }

        // Giữ lại các method cũ để tương thích backward
        public void ReviewUserLogin()
        {
            _ = Task.Run(async () => await ReviewUserLoginAsync());
        }

        public void Sestup()
        {
            _ = Task.Run(async () => await SetupAsync());
        }
    }
}
