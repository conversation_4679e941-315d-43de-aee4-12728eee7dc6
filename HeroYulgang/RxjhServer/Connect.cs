using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.GroupQuest;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;


public partial class Connect
{
	private System.Timers.Timer timer_0;

	private Socket socket_0;

	private byte[] dataReceive;

	public Connect()
	{
		dataReceive = new byte[102400];
		timer_0 = new(5000.0);
		timer_0.Elapsed += timer_0_Elapsed;
		timer_0.AutoReset = true;
		timer_0.Enabled = true;
	}

	private void timer_0_Elapsed(object sender, ElapsedEventArgs e)
	{
		if (!socket_0.Connected)
		{
			Sestup();
		}
	}

	public void Sestup()
	{
		try
		{
			IPEndPoint remoteEP = new(IPAddress.Parse(World.AccountVerificationServerIP), World.AccountVerificationServerPort);
			socket_0 = new(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
			socket_0.BeginConnect(remoteEP, method_0, socket_0);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Error connecting to the account verification server: " + World.AccountVerificationServerPort + " IP " + World.AccountVerificationServerIP + " " + ex.Message);
		}
	}

	public void Dispose()
	{
		if (timer_0 != null)
		{
			timer_0.Enabled = false;
			timer_0.Close();
			timer_0.Dispose();
		}
		try
		{
			socket_0.Shutdown(SocketShutdown.Both);
		}
		catch
		{
		}
		if (socket_0 != null)
		{
			socket_0.Close();
		}
		socket_0 = null;
	}

	public void ReviewUserLogin()
	{
		try
		{
			StringBuilder stringBuilder = new();
			foreach (var value9 in World.list.Values)
			{
				var value = "NULL";
				var value2 = 0;
				if (value9.TreoMay)
				{
					value2 = 1;
				}
				var value3 = 0;
				var value4 = string.Empty;
				var value5 = string.Empty;
				var value6 = 0;
				var value7 = string.Empty;
				var value8 = string.Empty;
				var players = World.FindPlayerBySession(value9.WorldId);
				if (players != null)
				{
					value = players.UserName;
					value3 = players.OriginalServerSerialNumber;
					value4 = players.OriginalServerIP;
					value5 = players.OriginalServerPort.ToString();
					value6 = players.OriginalServerID;
					value7 = players.SilverCoinSquareServerIP;
					value8 = players.SilverCoinSquareServerPort.ToString();
				}
				if (value9.Player == null)
				{
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Player Null;");
					return;
				}
				stringBuilder.Append(value9.Player.Userid);
				stringBuilder.Append("-");
				stringBuilder.Append(value9);
				stringBuilder.Append("-");
				stringBuilder.Append(value9.BindAccount);
				stringBuilder.Append("-");
				stringBuilder.Append(value2);
				stringBuilder.Append("-");
				stringBuilder.Append(value);
				stringBuilder.Append("-");
				stringBuilder.Append(value3);
				stringBuilder.Append("-");
				stringBuilder.Append(value4);
				stringBuilder.Append("-");
				stringBuilder.Append(value5);
				stringBuilder.Append("-");
				stringBuilder.Append(value6);
				stringBuilder.Append("-");
				stringBuilder.Append(value7);
				stringBuilder.Append("-");
				stringBuilder.Append(value8);
				stringBuilder.Append("-");
				stringBuilder.Append(value9.WorldId);
				stringBuilder.Append(",");
			}
			if (stringBuilder.Length > 0)
			{
				stringBuilder.Remove(stringBuilder.Length - 1, 1);
			}
			World.conn.Transmit("REVIEW_USER_LOGIN|" + stringBuilder);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Review Nguoi Choi Dang Nhap error: [" + ex.Message);
		}
	}

	private void method_0(IAsyncResult iasyncResult_0)
	{
		try
		{
			((Socket)iasyncResult_0.AsyncState).EndConnect(iasyncResult_0);
			try
			{
				Transmit("SERVER_CONNECT_LOGIN_X|" + World.ServerID + "|" + World.MaximumOnline + "|" + CRC32.GetEXECRC32());
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, "May chu tai khoan da ket noi thanh cong port: " + World.AccountVerificationServerPort + " - IP: - " + World.AccountVerificationServerIP);
				socket_0.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
				Thread.Sleep(500);
				Transmit("UPDATE_SERVER_PORT|" + World.ServerID + "|" + World.GameServerPort2);
				ReviewUserLogin();
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "May chu xac thuc ket noi loi goi lai： " + ex.Message);
			}
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi kết nối LoginServer - " + ex2.Message);
		}
	}

	public virtual void OnReceiveData(IAsyncResult iasyncResult_0)
	{
		try
		{
			var num = socket_0.EndReceive(iasyncResult_0);
			if (num > 0 && socket_0.Connected)
			{
				ProcessDataReceived(dataReceive, num);
				socket_0.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
			}
		}
		catch
		{
		}
	}

	public static byte[] Compress(byte[] byte_0)
	{
		try
		{
			MemoryStream memoryStream = new();
			GZipStream gZipStream = new(memoryStream, CompressionMode.Compress, leaveOpen: true);
			gZipStream.Write(byte_0, 0, byte_0.Length);
			gZipStream.Close();
			var array = new byte[memoryStream.Length];
			memoryStream.Position = 0L;
			memoryStream.Read(array, 0, array.Length);
			memoryStream.Close();
			return array;
		}
		catch (Exception ex)
		{
			throw new(ex.Message);
		}
	}

	public static string CompressString(string string_0)
	{
		return Convert.ToBase64String(Compress(Encoding.GetEncoding("UTF-8").GetBytes(string_0)));
	}

	public void Transmit(string string_0)
	{
		try
		{
			if (socket_0 != null && socket_0.Connected)
			{
				var bytes = Encoding.GetEncoding(1252).GetBytes(string_0);
				Send(bytes, bytes.Length);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Authentication server Transmiterror：" + string_0 + ex.Message);
		}
	}

	protected virtual void Send(byte[] byte_0, int int_0)
	{
		try
		{
			var array = new byte[int_0 + 6];
			array[0] = 204;
			array[1] = 153;
			Buffer.BlockCopy(BitConverter.GetBytes(int_0), 0, array, 2, 4);
			Buffer.BlockCopy(byte_0, 0, array, 6, int_0);
			socket_0.BeginSend(array, 0, int_0 + 6, SocketFlags.None, OnSended2, this);
		}
		catch (SocketException ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Account server Transmiterror：" + ex.Message);
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Account server Transmiterror：" + ex2.Message);
		}
	}

	public void OnSended2(IAsyncResult iasyncResult_0)
	{
		try
		{
			socket_0.EndSend(iasyncResult_0);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Account server Transmiterror：" + ex.Message);
		}
	}

	public void ProcessDataReceived(byte[] byte_0, int int_0)
	{
		try
		{
			var num = 0;
			if (204 == byte_0[0] && 153 == byte_0[1])
			{
				var array = new byte[4];
				System.Buffer.BlockCopy(byte_0, 2, array, 0, 4);
				//var num2 = BitConverter.ToInt32(array, 0);
				var num2 = BitConverter.ToInt32(array);
				if (int_0 < num2 + 6)
				{
					return;
				}
				while (true)
				{
					var array2 = new byte[num2];
					System.Buffer.BlockCopy(byte_0, num + 6, array2, 0, num2);
					num += num2 + 6;
					DataReceived(array2, num2);
					if (num >= int_0 || byte_0[num] != 204 || byte_0[num + 1] != 153)
					{
						break;
					}
					System.Buffer.BlockCopy(byte_0, num + 2, array, 0, 4);
					//num2 = BitConverter.ToInt16(array, 0);
					BitConverter.ToInt16(array);
				}
			}
			else
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "LoginServer in Chinese. Package：" + byte_0[0] + " " + byte_0[1]);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "error 12345：" + ex.Message);
			Console.WriteLine(ex.Message);
			Console.WriteLine(ex.Source);
			Console.WriteLine(ex.StackTrace);
		}
	}

	public void DataReceived(byte[] byte_0, int int_0)
	{
		var @string = Encoding.Default.GetString(byte_0);
		var string2 = Encoding.GetEncoding(1252).GetString(byte_0);
		try
		{
			var array = @string.Split('|');
			var array2 = string2.Split('|');
			var text = array[0];
			if (text == null)
			{
				return;
			}
			switch (text)
			{
				case "CROSS_SERVER_ZONE_INFO":
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, "Nhận thông tin tạo zone từ máy chủ khác");
					ProcessCrossServerZoneInfo(array);
					break;

				case "CROSS_SERVER_ZONE_REMOVE":
					ProcessCrossServerZoneRemove(array);
					break;

				case "CROSS_SERVER_ZONE_ACTION":
					ProcessCrossServerZoneAction(array);
					break;
				case "MAILCOD_ADMIN_NOTI":
					ProcessMailCodAdminNoti(array);
					break;

				case "CROSS_SERVER_ZONE_BROADCAST":
					ProcessCrossServerZoneBroadcast(array);
					break;
				case "CROSS_SERVER_ZONE_BROADCAST_FULL":
					ProcessCrossServerZoneBroadcastFull(array);
					break;

				case "CROSS_SERVER_ZONE_NPC_BROADCAST":
					ProcessCrossServerZoneNpcBroadcast(array);
					break;
				case "CROSS_SERVER_ZONE_NPC_BROADCAST_FULL":
					ProcessCrossServerZoneNpcBroadcastFull(array);
					break;

				case "GroupQuestMessage":
					var type = array[1]; // 1 = Guild, 2 = Faction, 3 = all
					var name = array[2];
					var message = array[3];
					var msgType = 1;
					int.TryParse(array[4], out msgType);
					var worldId = array[5];
					switch (type)
					{
						case "1":
							foreach (var item in World.allConnectedChars.Values)
							{
								if (item.GuildName == name)
								{
									item.HeThongNhacNho(message, msgType, "Thiên cơ các");
								}
							}
							break;
						case "2":
							var faction = 0;
							int.TryParse(name, out faction);
							foreach (var item in World.allConnectedChars.Values)
							{
								if (item.Player_Zx == faction)
								{
									item.HeThongNhacNho(message, msgType, "Thiên cơ các");
								}
							}
							break;
						case "3":
							foreach (var item in World.allConnectedChars.Values)
							{
								item.HeThongNhacNho(message, msgType, "Thiên cơ cách");
							}
							break;
					}
					break;
				case "ChatGuild":
					{
						if (!(array[1] == "OK"))
						{
							break;
						}
						var string_ = array[3];
						var text2 = array[5];
						var num = int.Parse(array[6]);
						var text3 = array[8];
						if (World.ServerID != num)
						{
							foreach (var value3 in World.allConnectedChars.Values)
							{
								if (!value3.Client.TreoMay)
								{
									if (value3.GuildName == text3)
									{
										value3.HeThongNhacNho("(Kênh " + num + ") " + text2, 3, string_);
									}
									else if (value3.GMMode != 0)
									{
										value3.HeThongNhacNho("[" + text3 + "](Kênh " + num + ") " + text2, 3, string_);
									}
								}
							}
							break;
						}
						if (World.ServerID != num)
						{
							break;
						}
						{
							foreach (var value4 in World.allConnectedChars.Values)
							{
								if (!value4.Client.TreoMay && value4.GuildName != text3 && value4.GMMode != 0)
								{
									value4.HeThongNhacNho("[" + text3 + "](Kênh " + num + ") " + text2, 3, string_);
								}
							}
							break;
						}
					}
				case "OPEN_TREASURE":
					MoRa_RuongVatPham(array[1], array[2], array[3]);
					break;
				case "DECREASE_FACTION_WAR":
					if (World.TheLucChien_Giam_NguoiChoi != null && !World.TheLucChien_Giam_NguoiChoi.ContainsKey(array[1]))
					{
						World.TheLucChien_Giam_NguoiChoi.Add(array[1], array[2]);
					}
					break;
				case "LION_ROAR":
					RoarPower(array[1], array[2], array[3]);
					break;
				case "LION_ROARX":
					// if (array[1]== "OK")
					// {
					// 	World.SendFullServerLionRoarMessageBroadcastData(int.Parse(array[2]), array[3], int.Parse(array[4]), array[5], int.Parse(array[6]), int.Parse(array[7]), int.Parse(array[8]));
					// }
					// else if (World.list.TryGetValue(int.Parse(array[2]), out NetState value2))
					// {
					// 	value2.Player.HeThongNhacNho("Sư tử hống vang trời, quần hùng tụ hội đông nghịt, xin đại hiệp kiên nhẫn chờ đợi...");
					// }
					// break;
				case "GET_SERVER_LIST":
					{
						var players2 = World.FindPlayerByName(array[1]);
						if (players2 != null)
						{
							for (var i = 2; i < array.Length - 1; i++)
							{
								players2.UpdateServerList(array[i]);
							}
						}
						break;
					}
				case "RDISCONNECTED_FACTION":
					if (World.TheLucChien_Giam_NguoiChoi != null && World.TheLucChien_Giam_NguoiChoi.ContainsKey(array[1]))
					{
						World.TheLucChien_Giam_NguoiChoi.Remove(array[1]);
					}
					break;
				case "TransmissionMessage":
					World.GuiDi_TruyenAm_TinTuc(int.Parse(array[1]), array[2], array[3], array[4], int.Parse(array[5]), array[6]);
					break;
				case "PK_MESSAGE":
					PKNhacNho(array[1], array2[2]);
					break;
				case "COUPLE_MESSAGE":
					CoupleTips(array[1], array[2]);
					break;
				case "UPDATE_CONFIGURATION_X":
					UpdateConfiguration(array[1], array[2]);
					break;
				case "FACTION_WAR_PROGRESS":
					World.TheLucChien_Progress = int.Parse(array[1]);
					break;
				case "FACTION_WAR_TOTAL":
					World.TheLucChien_ChinhPhai_SoNguoi = int.Parse(array[1]);
					World.TheLucChien_TaPhai_SoNguoi = int.Parse(array[2]);
					break;
				case "Guild_MESSAGE":
					var array4 = smethod_0(array[2]);
					World.SendGangMessage(array[1], array4, array4.Length);
					break;
				case "PVP":
					{
						for (var j = 1; j < array.Length; j++)
						{
							var players3 = World.KiemTra_Ten_NguoiChoi(array[j]);
							if (players3 != null)
							{
								players3.FLD_VIP = 1;
								players3.UpdateCharacterData(players3);
							}
						}
						break;
					}
				case "MONSTER_DROP":
					DanhQuai_RoiHop(array[1], array[2], array[3]);
					break;
				case "USER_KICKOUT":
					UserKicksOut(int.Parse(array[1]));
					break;
				case "ACCOUNT_SERVER_DISCONNECTED":
					socket_0?.Close();
					break;
				case "CHANGE_LINE_LOGIN":
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Người chơi đổi kênh: | " + @string + " | ");
					UserChangeLogin(int.Parse(array[4]), array[1], array[2], array[3], array[7], array[8], array[9], array[10], array[11], array[12], array[13], array[6], array[14]);

					break;
				case "USER_LOGIN_X":
					NguoiChoiDangNhap(int.Parse(array[2]), array[1], array[3], array[4], array[5], array[6], array[7], array[8], array[9]);
					break;
				case "OpClient":
				// TODO: Opclient
					// try
				// {
				// 	var players = World.FindPlayerBySession(int.Parse(array[1]));
				// 	NetState value;
				// 	if (players != null)
				// 	{
				// 		if (players.Client != null)
				// 		{
				// 			players.OpClient(int.Parse(array[2]));
				// 		}
				// 	}
				// 	else if (World.list.TryGetValue(int.Parse(array[1]), out value))
				// 	{
				// 		if (value.Player != null)
				// 		{
				// 			value.Player.OpClient(int.Parse(array[2]));
				// 			break;
				// 		}
				// 		var array3 = Converter.HexStringToByte("AA5512000100BB00040001000000000000000000000055AA");
				// 		System.Buffer.BlockCopy(BitConverter.GetBytes(int.Parse(array[2])), 0, array3, 10, 2);
				// 		System.Buffer.BlockCopy(BitConverter.GetBytes(int.Parse(array[1])), 0, array3, 4, 2);
				// 		value.Send(array3, array3.Length);
				// 	}
				// 	break;
				// }
				// catch
				// {
				// 	break;
				// }
				case "SEND_ANNOUCEMENT":
					GuiThongBao(int.Parse(array[1]), array[2]);
					break;
				case "USER_KICKOUTID":
					UserKicksOutID(array[1]);
					break;
				case "GROUP_QUEST_CONTRIBUTION":
					// Nhận thông tin đóng góp quest từ LS
					// Format: GROUP_QUEST_CONTRIBUTION|questType|questId|groupId|playerId|playerName|contributionCount
					ProcessGroupQuestContribution(array);
					break;
				case "GROUP_QUEST_COMPLETE":
					// Nhận thông báo hoàn thành quest từ LS
					// Format: GROUP_QUEST_COMPLETE|questType|questId|groupId
					ProcessGroupQuestComplete(array);
					break;
				case "GROUP_QUEST_PROGRESS":
					ProcessGroupQuestProgress(array);
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Verify that the server receives the error：" + @string + ex.Message);
		}
	}

	private static byte[] smethod_0(string string_0)
	{
		string_0 = string_0.Replace("      ", string.Empty);
		if (string_0.Length % 2 != 0)
		{
			string_0 += "      ";
		}
		var array = new byte[string_0.Length / 2];
		for (var i = 0; i < array.Length; i++)
		{
			array[i] = Convert.ToByte(string_0.Substring(i * 2, 2), 16);
		}
		return array;
	}

	public void UpdateConfiguration(string string_0, string string_1)
	{
		var players = World.FindPlayerByName(string_0);
		if (players != null)
		{
			if (players.ShortcutBar.Contains(1008000044))
			{
				players.CharactersToAddMax_HP += 200;
			}
			if (players.ShortcutBar.Contains(1008000045))
			{
				players.CharactersToAddMax_MP += 200;
			}
			if (!players.WhetherToUpdateTheConfiguration)
			{
				var array = Converter.HexStringToByte(string_1);
				System.Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 4, 2);
				players.ChangeTheLineToUpdateTheConfiguration(array, array.Length);
			}
			players.ClientSettings = string_1;
			players.CapNhat_HP_MP_SP();
		}
	}

	// Xử lý thông tin Zone liên server
	private void ProcessCrossServerZoneInfo(string[] data)
	{
		try
		{
			// Kiểm tra xem có đủ thông tin không
			if (data.Length < 10)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin Zone liên server");
				return;
			}

			// Lấy thông tin zone
			string originServerID = data[1];
			int zoneId = int.Parse(data[2]);
			int ownerPID = int.Parse(data[3]);
			int ownerType = int.Parse(data[4]);
			int accessCondition = int.Parse(data[5]);
			int mapId = int.Parse(data[6]);
			float centerX = float.Parse(data[7]);
			float centerY = float.Parse(data[8]);
			float radius = float.Parse(data[9]);
			int guildId = int.Parse(data[10]);

			// Kiểm tra xem zone đã tồn tại chưa
			var zoneManager = RxjhServer.ManageZone.ZoneManager.Instance;
			var existingZone = zoneManager.GetZone(zoneId);

			if (existingZone != null)
			{
				// Cập nhật thông tin zone
				existingZone.IsCrossServer = true;
				existingZone.AccessCondition = accessCondition;
				existingZone.OriginServerID = originServerID;

				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã cập nhật thông tin Zone liên server {zoneId} từ server {originServerID}");
			}
			else
			{
				// Tạo owner cho zone
				ManageZone.ZoneOwner owner = new ManageZone.ZoneOwner
				{
					PID = ownerPID,
					type = ownerType,
					name = ownerPID
				};

				// Tạo zone mới
				zoneManager.CreateCrossServerZone(owner, mapId, centerX, centerY, radius, accessCondition);

				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã tạo Zone liên server {zoneId} từ server {originServerID}");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý thông tin Zone liên server: {ex.Message}");
		}
	}

	// Xử lý xóa Zone liên server
	private void ProcessCrossServerZoneRemove(string[] data)
	{
		try
		{
			// Kiểm tra xem có đủ thông tin không
			if (data.Length < 3)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin xóa Zone liên server");
				return;
			}

			// Lấy thông tin zone
			string originServerID = data[1];
			int zoneId = int.Parse(data[2]);

			// Xóa zone
			var zoneManager = RxjhServer.ManageZone.ZoneManager.Instance;
			zoneManager.RemoveZone(zoneId);

			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã xóa Zone liên server {zoneId} từ server {originServerID}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý xóa Zone liên server: {ex.Message}");
		}
	}

	private void ProcessMailCodAdminNoti(string[] data) {
		try
		{
			int worldId = int.Parse(data[1]);
			int sessionId = int.Parse(data[2]);
			World.SendMailCodNotificationByAdmin(worldId,sessionId);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý ProcessMailCodAdminNoti: {ex.Message}");
		}
	}
	// Xử lý hành động trong Zone liên server
	private void ProcessCrossServerZoneAction(string[] data)
	{
		try
		{
			// Kiểm tra xem có đủ thông tin không
			if (data.Length < 5)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin hành động Zone liên server");
				return;
			}

			// Lấy thông tin hành động
			string originServerID = data[1];
			int zoneId = int.Parse(data[2]);
			string actionType = data[3];
			int sessionId = int.Parse(data[4]);

			// Xử lý hành động
			switch (actionType)
			{
				case "GET_NPCS":
					// Kiểm tra zone và gửi thông tin npc
					ProcessCrossServerGetNpcs(originServerID, zoneId, sessionId, data);
					break;
				case "GET_PLAYERS":
					// Kiểm tra zone và gửi thông tin player
					ProcessCrossServerGetPlayers(originServerID, zoneId, sessionId, data);
					break;
				case "REMOVE_PLAYER":
					// Xử lý xóa người chơi
					ProcessCrossServerRemovePlayer(originServerID, zoneId, sessionId);
					break;
				case "MOVE":
					// Xử lý di chuyển
					return;
					if (data.Length < 8)
					{
						LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin di chuyển Zone liên server");
						return;
					}

					float x = float.Parse(data[5]);
					float y = float.Parse(data[6]);
					int mapId = int.Parse(data[7]);

					ProcessCrossServerMove(originServerID, zoneId, sessionId, x, y, mapId);
					break;

                case "ATTACK_NPC":
                    // Xử lý tấn công NPC
                    ProcessCrossServerAttackNpc(originServerID, zoneId, sessionId, data);
                    break;
				case "NPC_DEATH":
                    // Xử lý NPC chết
                    ProcessCrossServerNpcDeath(originServerID, zoneId, sessionId, data);
                    break;

                case "CREATE_NPC_CLONE":
                    // Xử lý lấy thông tin NPC
                    ProcessCrossServerGetNpcInfo(originServerID, zoneId, sessionId, data);
                    break;
				case "CROSS_SERVER_ZONE_REQUEST":
					// Xử lý tạo zone cho máy chủ mới mở
					ProcessCrossServerCreateZone();
					break;
				case "PLAYER_MOVE_OUT_OF_MAP":
					// Xử lý người chơi di chuyển ra khỏi map
					ProcessCrossServerPlayerMoveOutOfMap(originServerID, zoneId, sessionId, data);
					break;
				default:
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không hỗ trợ hành động {actionType} trong Zone liên server");
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý hành động Zone liên server: {ex.Message}");
		}
	}

	private void ProcessCrossServerPlayerMoveOutOfMap(string originServerID, int zoneId, int sessionId, string[] data)
	{
		try
		{
			if (data.Length < 8)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin di chuyển ra khỏi map Zone liên server");
				return;
			}

			// Lấy zone
			var zoneManager = RxjhServer.ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);

			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId}");
				return;
			}
            //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $" {String.Join("|",data)}");
			int targetSessionId = int.Parse(data[4]);
			string playerName =data[6];
			int removedBeastId = int.Parse(data[7]);

			foreach (var player in zone.Players)
			{
				if (player != null && !player.Client.TreoMay)
				{
					World.DiChuyen_RaKhoi_BanDo(player, targetSessionId, removedBeastId);
				}
			}

			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Người chơi từ server {originServerID} di chuyển ra khỏi map trong Zone {zoneId}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý di chuyển ra khỏi map Zone liên server: {ex.Message}");
		}
	}



    private void ProcessCrossServerCreateZone()
    {
        try
        {
			// Lấy tất cả zone CrossServerr
			var zoneManager = RxjhServer.ManageZone.ZoneManager.Instance;
			var zones = zoneManager.GetAllZones();
			foreach (var zone in zones)
			{
				if (zone.IsCrossServer)
				{
					// Gửi thông tin zone đến LoginServer
					zoneManager.SendZoneInfoToLoginServer(zone, zoneManager.FindCircularZone(zone.ID));
				}
			}
			// TODO :
			// Đồng bộ NPC trong zone
			// Đồng bộ người chơi trong zone
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý tạo Zone liên server: {ex.Message}");
        }
    }

    // Xử lý tấn công trong Zone liên server
    private void ProcessCrossServerAttack(string originServerID, int zoneId, int sessionId, int targetType, int targetId)
	{
		try
		{
			// Lấy zone
			var zoneManager = RxjhServer.ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);

			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId}");
				return;
			}

			// Xử lý tấn công
			if (targetType == 0) // Player
			{
				// Tìm người chơi theo session
				var targetPlayer = World.FindPlayerBySession(targetId);

				if (targetPlayer == null)
				{
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy người chơi có session {targetId}");
					return;
				}

				// Gửi thông tin tấn công đến người chơi
				// Đây chỉ là giả lập, cần triển khai chi tiết hơn
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Người chơi từ server {originServerID} tấn công người chơi {targetPlayer.UserName}");
			}
			else if (targetType == 1) // NPC
			{
				// Tìm NPC theo ID
				var targetNPC = zone.NPCs.FirstOrDefault(n => n.ID == targetId);

				if (targetNPC == null)
				{
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy NPC có ID {targetId} trong Zone {zoneId}");
					return;
				}

				// Gửi thông tin tấn công đến NPC
				// Đây chỉ là giả lập, cần triển khai chi tiết hơn
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Người chơi từ server {originServerID} tấn công NPC {targetNPC.Name}");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý tấn công Zone liên server: {ex.Message}");
		}
	}
	private void ProcessCrossServerRemovePlayer(string originServerID, int zoneId, int sessionId)
	{
		try
		{
			// Lấy zone
			var zoneManager = RxjhServer.ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);
			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId}");
				return;
			}
			// Xóa người chơi
			foreach (var player in zone.Players)
			{
				var array = Converter.HexStringToByte("AA551600549C6300080001000000549C0001000000000000A37B55AA");
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.CharacterBeast.FullServiceID), 0, array, 14, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(sessionId), 0, array, 4, 2);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý xóa người chơi Zone liên server: {ex.Message}");
		}
	}

	private void ProcessCrossServerNpcDeath(string originServerID, int zoneId, int sessionId, string[] data)
	{
		try
		{
			if (data.Length < 7)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin NPC_DEATH Zone liên server");
				return;
			}

			int npcSessionId = int.Parse(data[5]);
			string npcWorldId = data[6];

			// Kiểm tra xem NPC có thuộc server hiện tại không
			if (npcWorldId == World.ServerID.ToString())
			{
				// Nếu là server gốc, tìm NPC theo sessionId
				NpcClass npc = null;
				foreach (var map in World.Map.Values)
				{
					if (map.npcTemplate.TryGetValue(npcSessionId, out var foundNpc))
					{
						npc = foundNpc;
						break;
					}
				}

				if (npc != null)
				{
					// Đảm bảo animation chết được hiển thị
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"NPC {npcSessionId} trên server gốc đã chết, gửi animation chết");
					npc.QuangBa_NPCDeathSoLieu();

					// Xử lý sự kiện chết nếu cần
					if (sessionId > 0)
					{
						var player = World.FindPlayerBySession(sessionId);
						if (player != null)
						{
							npc.OnNpcDeath(player);
						}
					}
				}
			}
			else
			{
				// Nếu là NPC clone, tìm trong danh sách NPC clone
				NpcClass npc = World.GetLocalNpcFromRemote(npcWorldId, npcSessionId);
				if (npc != null)
				{
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"NPC clone {npcSessionId} đã chết, gửi animation chết");
					// Gửi animation chết
					npc.QuangBa_NPCDeathSoLieu();

					// Xóa NPC khỏi zone và dispose
					var zone = npc.CurrentZone;
					if (zone != null)
					{
						zone.RemoveNpc(npc);
					}

					World.delNpc(npc.Rxjh_Map, npc.NPC_SessionID);
					npc.Dispose();
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý NPC_DEATH Zone liên server: {ex.Message}");
		}
	}

	private void ProcessCrossServerAttackNpc(string originServerID, int zoneId, int sessionId, string[] data)
	{
		try
		{
			if (data.Length < 9)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin ATTACK_NPC Zone liên server");
				return;
			}

			// Lấy zone
			var zoneManager = ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);

			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId}");
				return;
			}

			// Xử lý các thông tin cơ bản
			int npcSessionId = int.Parse(data[5]);
			string npcWorldId = data[6];
			char operation = data[7][0];
			int value = int.Parse(data[8]);

			// Thông tin người chơi (có thể không có đủ)
			int playerSession = 0;
			string playerName = "Unknown";
			int playerLevel = 1;
			string guildName = "";
			int guildId = 0;
			int npcLevel = 1;

			if (data.Length >= 10) playerSession = int.Parse(data[9]);
			if (data.Length >= 11) playerName = data[10];
			if (data.Length >= 12) playerLevel = int.Parse(data[11]);
			if (data.Length >= 13) guildName = data[12];
			if (data.Length >= 14) guildId = int.Parse(data[13]);
			if (data.Length >= 15) npcLevel = int.Parse(data[14]);

			// Tìm NPC dựa trên sessionId và worldId
			NpcClass npc = null;

			// Kiểm tra xem NPC có thuộc server hiện tại không
			
			npc = World.GetLocalNpcFromRemote(npcWorldId, npcSessionId);

			if (npc == null)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy NPC clone cho {npcWorldId}:{npcSessionId}");
				return;
			}

			// Cập nhật HP của NPC clone
			npc.UpdateHP(operation, value, playerSession, playerName, playerLevel, guildName, guildId, true);

			// Kiểm tra nếu HP = 0 và NPC chưa chết, gửi animation chết
			if (npc.Rxjh_HP <= 0 && !npc.NPCDeath)
			{
				// Đánh dấu NPC đã chết
				npc.NPCDeath = true;

				// Gửi animation chết
				npc.QuangBa_NPCDeathSoLieu();

				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"NPC clone {npc.Name} (ID: {npc.ID}) đã chết");
			}

			// Gửi thông tin cập nhật HP đến tất cả người chơi trong zone
			foreach (var p in zone.Players)
			{
				npc.UpdateNPCSoLieu(p);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý tấn công NPC Zone liên server: {ex.Message}");
		}
	}

	// Gửi thông tin tạo npc mới cho các zone
	public void CreateCloneNpc(int npcSessionId, string npcWorldId, int zoneId, NpcClass npc)
	{
		try
		{
			// Gửi yêu cầu lấy thông tin NPC từ server gốc
			// Chuyển float sang hex để đảm bảo độ chính xác
			var hexX = BitConverter.GetBytes(npc.Rxjh_cs_X);
			var hexY = BitConverter.GetBytes(npc.Rxjh_cs_Y);
			SendCrossServerAction(
				zoneId,
				"CREATE_NPC_CLONE",
				0, // Không cần sessionId của người chơi
				npcSessionId,
				npcWorldId,
				npc.FLD_PID,
				BitConverter.ToString(hexX).Replace("-", ""),
				BitConverter.ToString(hexY).Replace("-", ""),
				npc.Rxjh_Map,
				npc.Rxjh_HP,
				npc.Max_Rxjh_HP
			);

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi tạo NPC clone: {ex.Message}");
		}
	}

	private void ProcessCrossServerGetNpcInfo(string originServerID, int zoneId, int sessionId, string[] data)
	{
		try
		{
			if (data.Length < 6)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin CREATE_NPC_CLONE Zone liên server");
				return;
			}

			// Lấy thông tin NPC
			int npcSessionId = int.Parse(data[5]);
			string npcWorldId = data[6];
			int npcPID = int.Parse(data[7]);
			var nphX = data[8];
			var nphY = data[9];
			var npcX = Converter.HexStringToByte(nphX);
			var npcY = Converter.HexStringToByte(nphY);

			int npcMap = int.Parse(data[10]);
			int npcHP = int.Parse(data[11]);
			int npcMaxHP = int.Parse(data[12]);


			// Tìm NPC trong server hiện tại
			NpcClass npc = World.GetLocalNpcFromRemote(npcWorldId.ToString(), npcSessionId);
			if (npc == null)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy NPC có session {npcSessionId}");
				// Tạo mới NPC Clone
				NpcClass boss = World.AddNpcNeo(
                    npcPID,
                    BitConverter.ToSingle(npcX, 0),
                    BitConverter.ToSingle(npcY, 0),
                    npcMap,
                    "Boss Liên Server",
                    0, 0, 0, true, 0,
                    30 * 60 * 1000); // Thời gian tồn tại (ms)
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,"Set NpcWorld ID clone = "+ npcWorldId);
				boss.WorldId = npcWorldId;
				World.AddWorldBoss(boss);
				//Add vào zone
				World.AddCrossServerNpcMapping(npcWorldId, npcSessionId, boss.NPC_SessionID);
				ManageZone.ZoneManager.Instance.GetZone(zoneId).AddNpc(boss);
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info,$"Đã tạo NPC clone mới cho NPC {npcSessionId}");
				return;
			}

			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Clone Npc {npcSessionId} đã tồn tại");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý CREATE_NPC_CLONE Zone liên server: {ex.Message}");
		}
	}

	private void ProcessCrossServerGetNpcs(string originServerID, int zoneId, int sessionId, string[] data)
	{
		try
		{
			// TODO : Hoàn thiện get npc từ kênh khác
			// Lấy zone
			var zoneManager = RxjhServer.ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);
			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId}");
				return;
			}
			if (data.Length < 9)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin GET_PLAYERS Zone liên server");
				return;
			}

			// Lấy player ở zone và gửi thông tin broadcast
			Dictionary<int, NpcClass> npcs = new();
			foreach (var npc in zone.NPCs)
			{
				if (!npcs.TryGetValue(npc.NPC_SessionID, out var value))
				{
					npcs.Add(npc.NPC_SessionID, npc);
				}
			}
			using SendingClass sendingClass = new();
			sendingClass.Write4(npcs.Count);
			foreach (var value in npcs.Values)
			{
				sendingClass.Write4(value.NPC_SessionID);
				sendingClass.Write4(value.NPC_SessionID);
				sendingClass.Write2(value.FLD_PID);

				sendingClass.Write2(1);
				sendingClass.Write4(value.Rxjh_HP);
				sendingClass.Write4(value.Max_Rxjh_HP);
				//20
				sendingClass.Write(value.Rxjh_X);
				sendingClass.Write(value.Rxjh_Z);
				sendingClass.Write(value.Rxjh_Y);
				//+32
				sendingClass.Write4(1082130432);
				sendingClass.Write(value.FLD_FACE1);
				sendingClass.Write(value.FLD_FACE2);
				sendingClass.Write(value.Rxjh_X);
				sendingClass.Write(value.Rxjh_Z);
				sendingClass.Write(value.Rxjh_Y);
				sendingClass.Write4(0);
				// 32+28 = 60
				if (value.FLD_BOSS == 1)
				{
					sendingClass.Write4(1);
					sendingClass.Write4(10);
					sendingClass.Write2(0);
					sendingClass.Write2(0);
				}
				else
				{
					sendingClass.Write4(0);
					sendingClass.Write4(10);
					sendingClass.Write4(0);
				}
				sendingClass.Write4(2359296);
				sendingClass.Write4(uint.MaxValue);
				if (value.NPCDeath)
				{
					// value.UpdateNPC_DeXoaSoLieu(Playe);
					// value.LamMoi_NPCDeathSoLieu(Playe);
				}
			}
			string hexData = BitConverter.ToString(sendingClass.ToArray3()).Replace("-", "");
			string dataType = "NpcData";
			SendCrossServerBroadcast(zoneId, dataType, sessionId, hexData,26368,-1);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý GET_PLAYERS Zone liên server: {ex.Message}");
		}
	}

	private void ProcessCrossServerGetPlayers(string originServerID, int zoneId, int sessionId, string[] data)
	{
		try
		{
			// Lấy zone
			var zoneManager = RxjhServer.ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);
			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId}");
				return;
			}
			if (data.Length < 9)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin GET_PLAYERS Zone liên server");
				return;
			}

			// Lấy player ở zone và gửi thông tin broadcast
			foreach (var player in zone.Players)
			{
				if (player != null && !player.Client.TreoMay)
				{
					player.UpdateBroadcastCharacterData();
				}
			}


		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý GET_PLAYERS Zone liên server: {ex.Message}");
		}
	}

	// Xử lý di chuyển trong Zone liên server
	private void ProcessCrossServerMove(string originServerID, int zoneId, int sessionId, float x, float y, int mapId)
	{
		try
		{
			// Lấy zone
			var zoneManager = RxjhServer.ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);

			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId}");
				return;
			}

			// Xử lý di chuyển
			// Đây chỉ là giả lập, cần triển khai chi tiết hơn
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Người chơi từ server {originServerID} di chuyển đến ({x}, {y}) trong map {mapId}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý di chuyển Zone liên server: {ex.Message}");
		}
	}

	// Xử lý broadcast trong Zone liên server
	private void ProcessCrossServerZoneBroadcast(string[] data)
	{
		try
		{
			// Kiểm tra xem có đủ thông tin không
			if (data.Length < 6)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin broadcast Zone liên server");
				return;
			}

			// Lấy thông tin broadcast
			string originServerID = data[1];
			int zoneId = int.Parse(data[2]);
			string dataType = data[3];
			int sessionId = int.Parse(data[4]);
			string hexData = data[5];
			int id = int.Parse(data[6]);
			int wordid = int.Parse(data[7]);

			// Lấy zone
			var zoneManager = ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);

			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId} cho broadcast");
				// TODO: Tao zone
				// Gửi request tìm và tạo zone
				// SendCrossServerAction(zoneId, "ZONE_NOT_FOUND", sessionId, 0, 0, 0, 0);
				return;
			}

			// Chuyển đổi hexData thành byte array
			byte[] packetData = Converter.HexStringToByte(hexData);

			// Tạo SendingClass từ byte array
			SendingClass pak = new();
			pak.Write(packetData);

			BroadcastToZone(zone, pak, sessionId, id, wordid);

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý broadcast Zone liên server: {ex.Message}");
		}
	}
	private void ProcessCrossServerZoneBroadcastFull(string[] data)
	{
		try
		{
			// Kiểm tra xem có đủ thông tin không
			if (data.Length < 6)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin broadcast Zone liên server");
				return;
			}

			// Lấy thông tin broadcast
			string originServerID = data[1];
			int zoneId = int.Parse(data[2]);
			string dataType = data[3];
			int sessionId = int.Parse(data[4]);
			string hexData = data[5];

			// Lấy zone
			var zoneManager = ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);

			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId} cho broadcast");
				return;
			}

			// Chuyển đổi hexData thành byte array
			byte[] packetData = Converter.HexStringToByte(hexData);

			// Tạo SendingClass từ byte array
			BroadcastToZone(zone, hexData, sessionId);

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý broadcast Zone liên server: {ex.Message}");
		}
	}

	// Xử lý broadcast NPC trong Zone liên server
	private void ProcessCrossServerZoneNpcBroadcast(string[] data)
	{
		try
		{
			// Kiểm tra xem có đủ thông tin không
			if (data.Length < 6)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin NPC broadcast Zone liên server");
				return;
			}

			// Lấy thông tin broadcast
			string originServerID = data[1];
			int zoneId = int.Parse(data[2]);
			int npcId = int.Parse(data[3]);
			string dataType = data[4];
			string hexData = data[5];
			int id = int.Parse(data[6]);
			int wordid = int.Parse(data[7]);

			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Broadcast NPC {npcId} từ zone {zoneId} {id} {wordid}");

			// Lấy zone
			var zoneManager = ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);

			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId} cho NPC broadcast");
				// TODO :
				// Gửi request tìm và tạo zone
				return;
			}

			// Chuyển đổi hexData thành byte array
			byte[] packetData = Converter.HexStringToByte(hexData);

			// Tạo SendingClass từ byte array
			SendingClass pak = new();
			pak.Write(packetData);
			BroadcastNpcToZone(zone, pak, id, wordid);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý NPC broadcast Zone liên server: {ex.Message}");
		}
	}
	private void ProcessCrossServerZoneNpcBroadcastFull(string[] data)
	{
		try
		{
			// Kiểm tra xem có đủ thông tin không
			if (data.Length < 6)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin NPC broadcast Zone liên server");
				return;
			}

			// Lấy thông tin broadcast
			string originServerID = data[1];
			int zoneId = int.Parse(data[2]);
			int npcId = int.Parse(data[3]);
			string dataType = data[4];
			string hexData = data[5];

			// Lấy zone
			var zoneManager = ManageZone.ZoneManager.Instance;
			var zone = zoneManager.GetZone(zoneId);

			if (zone == null || !zone.IsCrossServer)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Không tìm thấy Zone liên server {zoneId} cho NPC broadcast");
				// TODO :
				// Gửi request tìm và tạo zone
				return;
			}

			// Chuyển đổi hexData thành byte array
			byte[] packetData = Converter.HexStringToByte(hexData);
			// TODO:
			// 	Kiểm tra Mapping để truyền đúng session

			BroadcastNpcToZone(zone, packetData);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi xử lý NPC broadcast Zone liên server: {ex.Message}");
		}
	}

	private void BroadcastToZone(ManageZone.Zone zone, string hexData, int excludeSessionId = 0)
	{
		try
		{

			if (zone == null || zone.Players == null || zone.Players.Count == 0)
				return;
			//LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Broadcast full packet từ zone khác");
			foreach (var player in zone.Players)
			{
				if (player != null && player.Client != null && player.Client.Running && !player.Client.TreoMay)
				{
					// Không gửi lại cho người gửi
					if (player.SessionID != excludeSessionId)
					{
						player.Client.Send_Map_Data(Converter.HexStringToByte(hexData), hexData.Length / 2);
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi broadcast đến zone: {ex.Message}");
		}
	}
	// Broadcast đến tất cả người chơi trong zone
	private void BroadcastToZone(ManageZone.Zone zone, SendingClass pak, int excludeSessionId = 0, int id = 0, int sessionId = 0)
	{
		try
		{

			if (zone == null || zone.Players == null || zone.Players.Count == 0)
				return;
			//LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Broadcast từ zone khác {id} {sessionId}");
			foreach (var player in zone.Players)
			{
				if (player != null && player.Client != null && player.Client.Running && !player.Client.TreoMay)
				{
					// Không gửi lại cho người gửi
					if (player.SessionID != excludeSessionId)
					{
						if (sessionId == -1)
						{
							sessionId = player.SessionID;
						}
						player.Client.SendPak(pak, id, sessionId);
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi broadcast đến zone: {ex.Message}");
		}
	}

	private void BroadcastNpcToZone(ManageZone.Zone zone, byte[] data)
	{
		try
		{
			if (zone == null || zone.Players == null || zone.Players.Count == 0)
				return;

			foreach (var player in zone.Players)
			{
				if (player != null && player.Client != null && player.Client.Running && !player.Client.TreoMay)
				{
					player.Client.Send_Map_Data(data, data.Length);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi broadcast NPC đến zone: {ex.Message}");
		}
	}

	// Broadcast NPC đến tất cả người chơi trong zone
	private void BroadcastNpcToZone(ManageZone.Zone zone, SendingClass pak, int id = 0, int wordid = 0)
	{
		try
		{
			if (zone == null || zone.Players == null || zone.Players.Count == 0)
				return;

			foreach (var player in zone.Players)
			{
				if (player != null && player.Client != null && player.Client.Running && !player.Client.TreoMay)
				{
					player.Client.SendPak(pak, id, wordid);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi broadcast NPC đến zone: {ex.Message}");
		}
	}

	// Gửi hành động trong Zone liên server đến LoginServer
	public void SendCrossServerAction(int zoneId, string actionType, int sessionId, params object[] parameters)
	{
		try
		{
			// Tạo chuỗi thông tin hành động
			StringBuilder sb = new StringBuilder();
			sb.Append("CROSS_SERVER_ZONE_ACTION|");
			sb.Append(World.ServerID);
			sb.Append("|");
			sb.Append(zoneId);
			sb.Append("|");
			sb.Append(actionType);
			sb.Append("|");
			sb.Append(sessionId);

			// Thêm các tham số
			foreach (var param in parameters)
			{
				sb.Append("|");
				sb.Append(param);
			}

			// Gửi thông tin hành động đến LoginServer
			Transmit(sb.ToString());
			if (actionType != "GET_NPCS")
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi hành động {actionType} trong Zone {zoneId} đến LoginServer");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi hành động Zone liên server: {ex.Message}");
		}
	}

	// Gửi dữ liệu broadcast trong Zone liên server đến LoginServer
	public void SendCrossServerBroadcast(int zoneId, string dataType, int sessionId, string hexData, int id, int wordid)
	{
		try
		{
			// Tạo chuỗi thông tin broadcast
			StringBuilder sb = new();
			sb.Append("CROSS_SERVER_ZONE_BROADCAST|");
			sb.Append(World.ServerID);
			sb.Append("|");
			sb.Append(zoneId);
			sb.Append("|");
			sb.Append(dataType);
			sb.Append("|");
			sb.Append(sessionId);
			sb.Append("|");
			sb.Append(hexData);
			sb.Append("|");
			sb.Append(id);
			sb.Append("|");
			sb.Append(wordid);

			// Gửi thông tin broadcast đến LoginServer
			Transmit(sb.ToString());

			//LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi broadcast {dataType} trong Zone {zoneId} đến LoginServer");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi broadcast Zone liên server: {ex.Message}");
		}
	}

	public void SendCrossServerBroadcast(int zoneId, string dataType, int sessionId, string hexData)
	{
		try
		{
			// Tạo chuỗi thông tin broadcast
			StringBuilder sb = new();
			sb.Append("CROSS_SERVER_ZONE_BROADCAST_FULL|");
			sb.Append(World.ServerID);
			sb.Append("|");
			sb.Append(zoneId);
			sb.Append("|");
			sb.Append(dataType);
			sb.Append("|");
			sb.Append(sessionId);
			sb.Append("|");
			sb.Append(hexData);
			sb.Append("|");

			// Gửi thông tin broadcast đến LoginServer
			Transmit(sb.ToString());

			//LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi broadcast {dataType} trong Zone {zoneId} đến LoginServer");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi broadcast Zone liên server: {ex.Message}");
		}
	}
	// Gửi dữ liệu broadcast NPC trong Zone liên server đến LoginServer
	public void SendCrossServerNpcBroadcast(int zoneId, int npcId, string dataType, string hexData, int id, int wordid)
	{
		try
		{
			// Tạo chuỗi thông tin broadcast
			StringBuilder sb = new();
			sb.Append("CROSS_SERVER_ZONE_NPC_BROADCAST|");
			sb.Append(World.ServerID);
			sb.Append("|");
			sb.Append(zoneId);
			sb.Append("|");
			sb.Append(npcId);
			sb.Append("|");
			sb.Append(dataType);
			sb.Append("|");
			sb.Append(hexData);
			sb.Append("|");
			sb.Append(id);
			sb.Append("|");
			sb.Append(wordid);

			// Gửi thông tin broadcast đến LoginServer
			Transmit(sb.ToString());

			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi NPC broadcast {dataType} trong Zone {zoneId} đến LoginServer");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi NPC broadcast Zone liên server: {ex.Message}");
		}
	}
	public void SendCrossServerNpcBroadcast(int zoneId, int npcId, string dataType, string hexData)
	{
		try
		{
			// Tạo chuỗi thông tin broadcast
			StringBuilder sb = new();
			sb.Append("CROSS_SERVER_ZONE_NPC_BROADCAST_FULL|");
			sb.Append(World.ServerID);
			sb.Append("|");
			sb.Append(zoneId);
			sb.Append("|");
			sb.Append(npcId);
			sb.Append("|");
			sb.Append(dataType);
			sb.Append("|");
			sb.Append(hexData);
			sb.Append("|");

			// Gửi thông tin broadcast đến LoginServer
			Transmit(sb.ToString());

			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi NPC broadcast {dataType} trong Zone {zoneId} đến LoginServer");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi NPC broadcast Zone liên server: {ex.Message}");
		}
	}

	public void NguoiChoiDangNhap(int int_0, string string_0, string string_1, string string_2, string string_3, string string_4, string string_5, string string_6, string string_7)
	{
		try
		{
			if (World.list.TryGetValue(int_0, out var value))
			{
				value.Player.KetNoi_DangNhap2(string_0, string_1, string_2, string_3, string_4, string_5, string_6, string_7);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Authentication server Nguoi Choi Dang Nhap error：" + ex.Message);
		}
	}

	public void UserChangeLogin(int int_0, string string_0, string string_1, string string_2, string string_3, string string_4, string string_5, string string_6, string string_7, string string_8, string string_9, string string_10, string string_11)
	{
		try
		{
			if (World.list.TryGetValue(int_0, out var value))
			{
				value.Player.ChangeLineAccountLogin(string_0, int.Parse(string_1), int.Parse(string_2), int_0, string_3, string_4, string_5, string_6, string_7, string_8, string_9, string_10, string_11);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Verify UserChangeLogin:" + ex.Message);
		}
	}

	public void RoarPower(string string_0, string string_1, string string_2)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value != null && !value.Client.TreoMay)
				{
					value.HeThongNhacNho(string_1, 21, string_0 + "：Lộ trình：" + string_2 + "Tuyến");
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "TransmitRoarPowererror:" + ex.Message);
		}
	}

	public void DanhQuai_RoiHop(string string_0, string string_1, string string_2)
	{
		try
		{
			World.ToanCucNhacNho("Thiên cơ các", 24, DateTime.Now.ToString("HH:mm|dd:MM:yyyy"));
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Transmit DanhQuai RoiHop error :" + ex.Message);
		}
	}

	public void CoupleTips(string string_0, string string_1)
	{
		try
		{
			World.ToanCucNhacNho("Thiên cơ các", 26, string_1);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Transmit CoupleTips error :" + ex.Message);
		}
	}

	public void PKNhacNho(string string_0, string TinNhan)
	{
		try
		{
			World.ToanCucNhacNho("Thiên cơ các", 24, TinNhan);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Truyền thông báo PKNhacNho Lỗi !! :" + ex.Message);
		}
	}

	public void MoRa_RuongVatPham(string string_0, string string_1, string string_2)
	{
		try
		{
			World.ToanCucNhacNho("MoRa_RuongVatPham:line:" + string_2 + "line", 22, string_1);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Transmit MoRa_Ruong Vat Pham error:" + ex.Message);
		}
	}

	/// <summary>
	/// Gửi cập nhật tiến trình quest đến LS
	/// </summary>
	/// <param name="questType">Loại quest (1: Guild, 2: Faction)</param>
	/// <param name="questId">ID của quest</param>
	/// <param name="groupId">ID của guild hoặc faction</param>
	/// <param name="currentCount">Số lượng hiện tại</param>
	/// <param name="status">Trạng thái quest</param>
	public void SendGroupQuestUpdate(int questType, int questId, int groupId, int currentCount, int status)
	{
		try
		{
			Transmit($"GROUP_QUEST_UPDATE|{questType}|{questId}|{groupId}|{currentCount}|{status}");
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi cập nhật quest đến LS: Type {questType}, Quest ID {questId}, Group ID {groupId}, Count {currentCount}, Status {status}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi gửi cập nhật quest đến LS: {ex.Message}");
		}
	}

	/// <summary>
	/// Gửi thông tin đóng góp quest đến LS
	/// </summary>
	/// <param name="questType">Loại quest (1: Guild, 2: Faction)</param>
	/// <param name="questId">ID của quest</param>
	/// <param name="groupId">ID của guild hoặc faction</param>
	/// <param name="playerId">ID của người chơi</param>
	/// <param name="playerName">Tên người chơi</param>
	/// <param name="contributionCount">Số lượng đóng góp</param>
	public void SendGroupQuestContribution(int questType, int npcId, int npcLevel, int groupId, int playerId, string playerName, int playerLevel, int contributionCount)
	{
		try
		{
			Transmit($"MONSTER_KILL|{questType}|{npcId}|{npcLevel}|{groupId}|{playerId}|{playerName}|{playerLevel}|{contributionCount}|{World.ServerID}");
			//LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi đóng góp quest đến LS: Type {questType}, NpcId {npcId},Npc Level {npcLevel}, Group ID {groupId}, Player ID {playerId}, Count {contributionCount}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi gửi đóng góp quest đến LS: {ex.Message}");
		}
	}

	public void SendPlayerKill(Players victim, Players killer, Players[] contributors)
	{
		try
		{
			var message = $"PLAYER_KILL|{World.ServerID}|{victim.SessionID}|{victim.UserName}|{victim.Player_Level}|{victim.GuildId}|{victim.Player_Zx}|{World.ServerID}|{killer.SessionID}|{killer.UserName}|{killer.Player_Level}|{killer.GuildId}|{killer.Player_Zx}";
			foreach (var contributor in contributors)
			{
				if (contributor == null || contributor == killer || contributor == victim)
					continue;
				message += $"|{World.ServerID}|{contributor.SessionID}|{victim.UserName}|{contributor.Player_Level}|{contributor.GuildId}|{contributor.Player_Zx}";
			}
			Transmit(message);
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi thông tin tiêu diệt người chơi đến LS: Killer ID {killer.SessionID}, Victim ID {victim.SessionID}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi gửi thông tin tiêu diệt người chơi đến LS: {ex.Message}");
		}
	}

	/// <summary>
	/// Gửi thông báo hoàn thành quest đến LS
	/// </summary>
	/// <param name="questType">Loại quest (1: Guild, 2: Faction)</param>
	/// <param name="questId">ID của quest</param>
	/// <param name="groupId">ID của guild hoặc faction</param>
	public void SendGroupQuestComplete(int questType, int questId, int groupId)
	{
		try
		{
			Transmit($"GROUP_QUEST_COMPLETE|{questType}|{questId}|{groupId}");
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi hoàn thành quest đến LS: Type {questType}, Quest ID {questId}, Group ID {groupId}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi gửi hoàn thành quest đến LS: {ex.Message}");
		}
	}


	/// <summary>
	/// Xử lý thông tin đóng góp quest từ LS
	/// </summary>
	/// <param name="array">Mảng chứa thông tin đóng góp</param>
	private void ProcessGroupQuestContribution(string[] array)
	{
		try
		{
			if (array.Length < 7)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin đóng góp quest");
				return;
			}

			int questType = int.Parse(array[1]);
			int targetId = int.Parse(array[2]);
			int targetLevel = int.Parse(array[3]);
			int groupId = int.Parse(array[4]);
			int playerId = int.Parse(array[5]);
			string playerName = array[6];
			int contributionCount = int.Parse(array[7]);

			// Thông báo cho người chơi
			string questTypeName = questType == 1 ? "bang hội" : "phe phái";
			string groupName = questType == 1 ? $"Bang hội ID {groupId}" : (groupId == 1 ? "Chính Phái" : "Tà Phái");

			// Gửi thông báo đến người chơi đã đóng góp
			Players contributor = World.FindPlayerBySession(playerId);
			if (contributor != null && !contributor.Client.TreoMay)
			{
				contributor.HeThongNhacNho($"Bạn đã đóng góp {contributionCount} cho nhiệm vụ {questTypeName} #{targetId}!", 10, "");
			}

			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã nhận đóng góp quest từ LS: Type {questType}, Quest ID {targetId}, Group ID {groupId}, Player ID {playerId}, Count {contributionCount}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý đóng góp quest: {ex.Message}");
		}
	}

	/// <summary>
	/// Xử lý thông báo hoàn thành quest từ LS
	/// </summary>
	/// <param name="array">Mảng chứa thông tin hoàn thành</param>
	private void ProcessGroupQuestComplete(string[] array)
	{
		try
		{
			if (array.Length < 3)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin hoàn thành quest");
				return;
			}

			int guildId = int.Parse(array[1]);
			var message = array[2];
			int questId = int.Parse(array[3]);
			int progressId = int.Parse(array[4]);
			int progressCount = int.Parse(array[5]);

			foreach (var player in World.allConnectedChars.Values)
			{
				if (player != null && !player.Client.TreoMay)
				{
					if (player.GuildId == guildId)
					{
						player.HeThongNhacNho($"#{questId}-{World.FromBase64(message)} đã hoàn thành!!", 10, "");
						if (World.IsGuildMaster(player))
						{
							if (player.QuestList.ContainsKey(questId))
								player.QuestList.Remove(questId);
							player.TaskPromptDataSending(questId, 51, 1);
						}
						//TODO:
						// Chia phần thưởng
						GroupQuestEvent.Instance.ReceiveGuildQuestReward(player, questId);
					}
				}

			}

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý hoàn thành quest: {ex.Message}");
		}
	}



	/// <summary>
	/// Kiểm tra xem người chơi có đóng góp cho quest không
	/// </summary>
	public bool CheckPlayerContribution(int questType, int questId, int groupId, int playerId)
	{
		try
		{
			// Trong thực tế, chúng ta sẽ gửi request đến LS để kiểm tra
			// Ở đây chỉ giả lập việc kiểm tra
			return true;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi kiểm tra đóng góp quest: {ex.Message}");
			return false;
		}
	}

	/// <summary>
	/// Kiểm tra xem người chơi đã nhận thưởng chưa
	/// </summary>
	public bool CheckRewardReceived(int questType, int questId, int groupId, int playerId)
	{
		try
		{
			// Trong thực tế, chúng ta sẽ gửi request đến LS để kiểm tra
			// Ở đây chỉ giả lập việc kiểm tra
			return false;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi kiểm tra nhận thưởng quest: {ex.Message}");
			return false;
		}
	}

	/// <summary>
	/// Đánh dấu người chơi đã nhận thưởng
	/// </summary>
	public void MarkRewardReceived(int questType, int questId, int groupId, int playerId)
	{
		try
		{
			// Gửi thông tin đến LS để đánh dấu đã nhận thưởng
			Transmit($"GROUP_QUEST_MARK_REWARD|{questType}|{questId}|{groupId}|{playerId}");
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi đánh dấu nhận thưởng đến LS: Type {questType}, Quest ID {questId}, Group ID {groupId}, Player ID {playerId}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi đánh dấu nhận thưởng quest: {ex.Message}");
		}
	}


	public void GuiThongBao(int int_0, string string_0)
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value != null && !value.Client.TreoMay)
				{
					switch (int_0)
					{
						case 0:
							value.SystemNotification(string_0);
							break;
						case 1:
							value.SystemRollingAnnouncement(string_0);
							break;
						case 2:
							value.HeThongNhacNho(string_0, 10, "Truyền Âm Các");
							break;
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Gui Thong Bao error:" + ex.Message);
		}
	}

	public void UserKicksOut(int int_0)
	{
		try
		{
			var players = World.FindPlayerBySession(int_0);
			var text = string.Empty;
			var text2 = string.Empty;
			var text3 = string.Empty;
			if (players != null)
			{
				text = players.Userid;
				text2 = players.UserName;
				text3 = players.Client.ToString();
				if (players.Client.TreoMay)
				{
					players.Client.DisposedOffline();
					if (players.Offline_TreoMay_Mode_ON_OFF != 0)
					{
						World.TreoMay_Offline--;
						if (World.TreoMay_Offline < 0)
						{
							World.TreoMay_Offline = 0;
						}
					}
					else
					{
						World.OffLine_SoLuong--;
						if (World.OffLine_SoLuong < 0)
						{
							World.OffLine_SoLuong = 0;
						}
					}
				}
				else
				{
					players.kickidlog("UserKicksOut()");
					players.OpClient(1);
					players.Logout();
					players.Client.Dispose();
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Disconnected![Mã dis 01]");
					logo.logdis("Disconnected![Mã dis 01]");
				}
			}
			if (World.list.TryGetValue(int_0, out var value))
			{
				World.delWorldId(int_0);
				value.Dispose();
			}
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Người dùng Kick Out: [" + text + "]-[" + text2 + "]" + text3);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Authentication server User Kicks Out error:" + int_0 + " " + ex.Message);
		}
	}

	public void UserKicksOutID(string string_0)
	{
		try
		{
			var players = World.FindPlayerByName(string_0);
			if (players == null)
			{
				return;
			}
			var userid = players.Userid;
			var userName = players.UserName;
			players.Client.ToString();
			if (players.Client.TreoMay)
			{
				players.Client.DisposedOffline();
				if (players.Offline_TreoMay_Mode_ON_OFF != 0)
				{
					World.TreoMay_Offline--;
					if (World.TreoMay_Offline < 0)
					{
						World.TreoMay_Offline = 0;
					}
				}
				else
				{
					World.OffLine_SoLuong--;
					if (World.OffLine_SoLuong < 0)
					{
						World.OffLine_SoLuong = 0;
					}
				}
			}
			else
			{
				players.OpClient(1);
				players.kickidlog("Player repeats Login      -      UserKicksOutID()");
				players.Logout();
				players.Client.Dispose();
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Kick dis logout");
			}
			World.conn.Transmit("KICK_PLAYER_ID|" + World.ServerID + "|" + string_0);
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, "Đăng nhập cộng dồn lần 2 - [" + players.Userid + "]-[" + players.UserName + "]" + players.Client);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Authentication server UserKicksOuterror:" + string_0 + " hiu " + ex.Message);
		}
	}
	public void ProcessGroupQuestProgress(string[] array)
	{
		// client.Sendd($"GROUP_QUEST_PROGRESS|{contribution.PlayerID}|{contribution.PlayerName}|{questDef.QuestName}|{quest.CurrentCount}|{contribution.ContributionCount}|{questDef.TargetCount}");
		try
		{
			if (array.Length < 6)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin GROUP_QUEST_PROGRESS");
				return;
			}
			int playerId = int.Parse(array[1]);
			string playerName = array[2];
			string questName = array[3];
			int currentCount = int.Parse(array[4]);
			int targetCount = int.Parse(array[5]);
			int contributionCount = int.Parse(array[6]);
			Players player = World.KiemTra_Ten_NguoiChoi(playerName);
			if (player != null && !player.Client.TreoMay)
            {
                player.HeThongNhacNho($"[{World.FromBase64(questName)}] :[{currentCount}/{contributionCount}] ({targetCount})!", 10, "");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý GROUP_QUEST_PROGRESS: {ex.Message}");
		}
	}
}
